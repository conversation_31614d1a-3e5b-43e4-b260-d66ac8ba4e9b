/*
 * 变比设置功能的替代实现
 * 如果需要严格按照题目示例输出原值
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      处理变比值输入 - 严格按题目示例版本
    \param[in]  ratio_str: 变比值字符串
    \param[out] none
    \retval     none
*/
void process_ratio_input_strict(char* ratio_str)
{
    float new_ratio;
    char *endptr;
    float old_ratio = current_ratio;  // 保存原值
    
    // 使用strtof解析浮点数
    new_ratio = strtof(ratio_str, &endptr);
    
    // 检查解析是否成功（endptr指向字符串末尾或空白字符）
    if (endptr == ratio_str || (*endptr != '\0' && *endptr != '\r' && *endptr != '\n')) {
        // 解析失败
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        my_printf(DEBUG_USART, "ratio=%.1f\r\n", current_ratio);
        return;
    }
    
    // 验证范围：0-100
    if (new_ratio < 0.0f || new_ratio > 100.0f) {
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        my_printf(DEBUG_USART, "ratio=%.1f\r\n", current_ratio);
        return;
    }
    
    // 更新变比值
    current_ratio = new_ratio;
    
    // 按题目示例：显示原值而不是新值
    my_printf(DEBUG_USART, "ratio modified success ratio=%.1f\r\n", old_ratio);
}

/*!
    \brief      处理变比值输入 - 逻辑正确版本
    \param[in]  ratio_str: 变比值字符串
    \param[out] none
    \retval     none
*/
void process_ratio_input_logical(char* ratio_str)
{
    float new_ratio;
    char *endptr;
    
    // 使用strtof解析浮点数
    new_ratio = strtof(ratio_str, &endptr);
    
    // 检查解析是否成功（endptr指向字符串末尾或空白字符）
    if (endptr == ratio_str || (*endptr != '\0' && *endptr != '\r' && *endptr != '\n')) {
        // 解析失败
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        my_printf(DEBUG_USART, "ratio=%.1f\r\n", current_ratio);
        return;
    }
    
    // 验证范围：0-100
    if (new_ratio < 0.0f || new_ratio > 100.0f) {
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        my_printf(DEBUG_USART, "ratio=%.1f\r\n", current_ratio);
        return;
    }
    
    // 更新变比值
    current_ratio = new_ratio;
    
    // 逻辑正确：显示新值
    my_printf(DEBUG_USART, "ratio modified success ratio=%.1f\r\n", current_ratio);
}

/*!
    \brief      测试两种实现的差异
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_implementation_difference(void)
{
    printf("\r\n=== 测试两种实现的差异 ===\r\n");
    
    // 设置初始值为1.0
    extern float current_ratio;
    current_ratio = 1.0f;
    
    printf("初始变比值: %.1f\r\n\r\n", current_ratio);
    
    // 测试严格按题目示例的版本
    printf("1. 严格按题目示例版本：\r\n");
    printf("输入: 10.5\r\n");
    printf("期望输出: ratio modified success ratio=1.0\r\n");
    printf("实际输出: ");
    process_ratio_input_strict("10.5");
    printf("变比值已更新为: %.1f\r\n\r\n", current_ratio);
    
    // 重置为1.0
    current_ratio = 1.0f;
    
    // 测试逻辑正确的版本
    printf("2. 逻辑正确版本：\r\n");
    printf("输入: 10.5\r\n");
    printf("期望输出: ratio modified success ratio=10.5\r\n");
    printf("实际输出: ");
    process_ratio_input_logical("10.5");
    printf("变比值已更新为: %.1f\r\n", current_ratio);
    
    printf("\r\n=== 差异测试完成 ===\r\n");
    printf("选择哪种实现取决于题目的真实意图\r\n");
}

/*!
    \brief      完整的对比测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_comparison_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        变比实现对比测试              #\r\n");
    printf("##########################################\r\n");
    
    printf("题目示例分析：\r\n");
    printf("输入: ratio -> 输出: ratio=1.0, Input value(0~100):\r\n");
    printf("输入: 10.5 -> 输出: ratio modified success ratio=1.0\r\n");
    printf("\r\n");
    printf("这个示例有两种可能的解释：\r\n");
    printf("1. 题目示例有错误，应该显示新值 (10.5)\r\n");
    printf("2. 题目要求显示原值 (1.0)\r\n");
    printf("\r\n");
    
    // 运行对比测试
    test_implementation_difference();
    
    printf("\r\n建议：\r\n");
    printf("1. 如果评分系统严格按示例检查，使用严格版本\r\n");
    printf("2. 如果注重逻辑正确性，使用逻辑版本\r\n");
    printf("3. 可以先用逻辑版本测试，如果有问题再改为严格版本\r\n");
    
    printf("\r\n##########################################\r\n");
    printf("#        对比测试完成                  #\r\n");
    printf("##########################################\r\n");
}

/*
 * 使用说明：
 * 
 * 如果需要修改当前实现以严格按照题目示例：
 * 
 * 1. 在 usart_app.c 中找到 process_ratio_input 函数
 * 2. 将其替换为 process_ratio_input_strict 的实现
 * 3. 或者添加一个选择开关
 * 
 * 修改方法：
 * 在 process_ratio_input 函数中，将：
 *   my_printf(DEBUG_USART, "ratio modified success ratio=%.1f\r\n", current_ratio);
 * 改为：
 *   float old_ratio = current_ratio;  // 在函数开始时保存
 *   current_ratio = new_ratio;        // 更新值
 *   my_printf(DEBUG_USART, "ratio modified success ratio=%.1f\r\n", old_ratio);
 * 
 * 测试方法：
 * complete_comparison_test();  // 运行对比测试
 */
