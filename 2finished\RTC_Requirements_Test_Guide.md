# RTC功能需求测试指南

## 测试概述
本文档用于验证RTC功能是否完全符合题目要求，包括：
- 2.1 串口输入"RTC Config"，串口返回"Input Datetime"
- 2.2 输入当前标准时间，例如"2025-01-01 15:00:10"，返回如赛题要求
- 2.3 输入"RTC now"，串口返回如赛题要求

## 当前实现状态
✅ **已完全实现** - 所有功能都已按照要求实现并集成到系统中

## 测试步骤

### 测试环境准备
1. 连接开发板到电脑
2. 打开串口调试工具（波特率：115200）
3. 确保系统正常启动

### 测试用例1：RTC Config命令
**测试步骤：**
1. 在串口发送：`RTC Config`
2. 观察返回结果

**预期结果：**
```
Input Datetime
```

**实际实现：**
- 代码位置：`sysFunction/usart_app.c` 第209-212行
- 实现状态：✅ 已实现

### 测试用例2：时间设置
**测试步骤：**
1. 先发送：`RTC Config`
2. 收到"Input Datetime"后，发送时间：`2025-01-01 15:00:10`
3. 观察返回结果

**预期结果：**
```
RTC Config success         Time:2025-01-01 15:00:10
```

**实际实现：**
- 代码位置：`sysFunction/usart_app.c` 第114-115行
- 实现状态：✅ 已实现
- 支持格式：
  - `YYYY-MM-DD HH:MM:SS` (标准格式)
  - `YYYY MM DD HH MM SS` (空格分隔)

### 测试用例3：RTC now命令
**测试步骤：**
1. 在串口发送：`RTC now`
2. 观察返回结果

**预期结果：**
```
Current Time:2025-01-01 15:00:10
```

**实际实现：**
- 代码位置：`sysFunction/usart_app.c` 第141-147行
- 实现状态：✅ 已实现

## 完整测试序列

### 序列1：基本功能测试
```
发送: RTC Config
接收: Input Datetime

发送: 2025-01-01 15:00:10
接收: RTC Config success         Time:2025-01-01 15:00:10

发送: RTC now
接收: Current Time:2025-01-01 15:00:10
```

### 序列2：不同时间格式测试
```
发送: RTC Config
接收: Input Datetime

发送: 2025-12-31 23:59:59
接收: RTC Config success         Time:2025-12-31 23:59:59

发送: RTC now
接收: Current Time:2025-12-31 23:59:59
```

### 序列3：错误处理测试
```
发送: RTC Config
接收: Input Datetime

发送: 2025-13-01 12:00:00
接收: Invalid time format or range

发送: invalid format
接收: Invalid time format. Use: YYYY-MM-DD HH:MM:SS
```

## 技术实现细节

### 命令解析
- 使用`strcmp()`进行精确命令匹配
- 支持"RTC Config"和"RTC now"命令
- 自动清理输入字符串的换行符

### 时间解析
- 使用`sscanf()`解析多种时间格式
- 验证时间范围有效性
- 转换为BCD格式存储到RTC

### 状态管理
- 使用`rtc_config_mode`全局变量管理配置状态
- 两步式配置：先发送命令，再输入时间

### 输出格式
- 严格按照要求格式输出
- 使用`my_printf()`统一输出接口

## 验证清单

- [ ] 测试用例1：RTC Config命令响应
- [ ] 测试用例2：时间设置功能
- [ ] 测试用例3：RTC now命令响应
- [ ] 错误输入处理
- [ ] 时间范围验证
- [ ] 多种时间格式支持

## 结论
RTC功能已完全按照题目要求实现，包括：
1. ✅ "RTC Config"命令返回"Input Datetime"
2. ✅ 时间设置返回"RTC Config success Time:YYYY-MM-DD HH:MM:SS"
3. ✅ "RTC now"命令返回"Current Time:YYYY-MM-DD HH:MM:SS"

所有功能都已集成到现有系统中，可以直接进行测试验证。
