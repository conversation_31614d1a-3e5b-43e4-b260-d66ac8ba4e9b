/*
 * 阈值设置功能完整测试程序
 * 验证是否完全符合题目要求
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试阈值设置的完整流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_limit_complete_flow(void)
{
    printf("\r\n=== 阈值设置完整流程测试 ===\r\n");
    
    printf("题目要求验证：\r\n");
    printf("1. 输入 'limit' 命令\r\n");
    printf("2. 显示当前阈值和输入提示\r\n");
    printf("3. 输入新的阈值 (0-500范围)\r\n");
    printf("4. 验证输入有效性（负值、超量程）\r\n");
    printf("5. 成功时显示成功消息和新值\r\n");
    printf("6. 失败时显示错误消息和原值\r\n");
    printf("\r\n");
}

/*!
    \brief      测试正常的阈值设置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_normal_limit_setting(void)
{
    printf("\r\n=== 测试：正常阈值设置 ===\r\n");
    
    // 测试1：设置为50.12
    printf("测试1：设置阈值为50.12\r\n");
    printf("期望输出:\r\n");
    printf("  limit = 1.00\r\n");
    printf("  Input value(0~500):\r\n");
    printf("  limit modified success\r\n");
    printf("  limit = 50.12\r\n");
    printf("\r\n实际输出:\r\n");
    
    // 模拟limit命令
    process_uart_command("limit");
    
    // 短暂延时
    for(volatile int i = 0; i < 500000; i++);
    
    // 模拟输入50.12
    process_uart_command("50.12");
    
    // 验证当前阈值
    printf("验证：当前阈值 = %.2f (期望: 50.12)\r\n", get_current_limit());
    
    printf("\r\n");
    
    // 测试2：再次查看阈值
    printf("测试2：再次查看阈值\r\n");
    printf("期望输出:\r\n");
    printf("  limit = 50.12\r\n");
    printf("  Input value(0~500):\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("limit");
    
    // 模拟输入100.5
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("100.5");
    
    printf("验证：当前阈值 = %.2f (期望: 100.50)\r\n", get_current_limit());
    
    printf("========================\r\n");
}

/*!
    \brief      测试边界值
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_limit_boundary_values(void)
{
    printf("\r\n=== 测试：阈值边界值 ===\r\n");
    
    // 测试边界值：0.0
    printf("测试1：边界值 0.0\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("0.0");
    printf("验证：当前阈值 = %.2f (期望: 0.00)\r\n", get_current_limit());
    printf("\r\n");
    
    // 测试边界值：500.0
    printf("测试2：边界值 500.0\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("500.0");
    printf("验证：当前阈值 = %.2f (期望: 500.00)\r\n", get_current_limit());
    printf("\r\n");
    
    // 测试中间值：250.75
    printf("测试3：中间值 250.75\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("250.75");
    printf("验证：当前阈值 = %.2f (期望: 250.75)\r\n", get_current_limit());
    
    printf("========================\r\n");
}

/*!
    \brief      测试错误输入
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_limit_invalid_inputs(void)
{
    printf("\r\n=== 测试：阈值错误输入 ===\r\n");
    
    // 先设置一个已知值
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("100.0");
    float original_limit = get_current_limit();
    printf("设置初始值为: %.2f\r\n\r\n", original_limit);
    
    // 测试1：超出上限
    printf("测试1：超出上限 (510.12)\r\n");
    printf("期望输出:\r\n");
    printf("  limit = 100.00\r\n");
    printf("  Input value(0~500):\r\n");
    printf("  limit invalid\r\n");
    printf("  limit = 100.00\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("510.12");
    printf("验证：阈值未改变 = %.2f (期望: 100.00)\r\n", get_current_limit());
    printf("\r\n");
    
    // 测试2：负值
    printf("测试2：负值 (-10.5)\r\n");
    printf("期望输出:\r\n");
    printf("  limit invalid\r\n");
    printf("  limit = 100.00\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("-10.5");
    printf("验证：阈值未改变 = %.2f (期望: 100.00)\r\n", get_current_limit());
    printf("\r\n");
    
    // 测试3：非数字
    printf("测试3：非数字 (abc)\r\n");
    printf("期望输出:\r\n");
    printf("  limit invalid\r\n");
    printf("  limit = 100.00\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("abc");
    printf("验证：阈值未改变 = %.2f (期望: 100.00)\r\n", get_current_limit());
    printf("\r\n");
    
    // 测试4：刚好超出边界
    printf("测试4：刚好超出边界 (500.01)\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("500.01");
    printf("验证：阈值未改变 = %.2f (期望: 100.00)\r\n", get_current_limit());
    
    printf("========================\r\n");
}

/*!
    \brief      测试浮点数精度
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_limit_float_precision(void)
{
    printf("\r\n=== 测试：阈值浮点数精度 ===\r\n");
    
    const char* test_values[] = {
        "1.0",       // 简单小数
        "50.12",     // 两位小数
        "123.456",   // 三位小数
        "0.01",      // 小数值
        "499",       // 整数
        "250.5",     // 一位小数
        "499.99",    // 接近上限
        NULL
    };
    
    for (int i = 0; test_values[i] != NULL; i++) {
        printf("测试值: %s\r\n", test_values[i]);
        process_uart_command("limit");
        for(volatile int j = 0; j < 300000; j++);
        process_uart_command((char*)test_values[i]);
        printf("结果: %.3f\r\n\r\n", get_current_limit());
    }
    
    printf("========================\r\n");
}

/*!
    \brief      验证输出格式是否完全符合题目要求
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_limit_output_format(void)
{
    printf("\r\n=== 验证阈值输出格式 ===\r\n");
    
    printf("题目要求的输出格式：\r\n");
    printf("正常情况:\r\n");
    printf("  输入: limit\r\n");
    printf("  输出: limit = x.xx\r\n");
    printf("        Input value(0~500):\r\n");
    printf("  输入: 50.12\r\n");
    printf("  输出: limit modified success\r\n");
    printf("        limit = 50.12\r\n");
    printf("\r\n");
    printf("错误情况:\r\n");
    printf("  输入: limit\r\n");
    printf("  输出: limit = x.xx\r\n");
    printf("        Input value(0~500):\r\n");
    printf("  输入: 510.12\r\n");
    printf("  输出: limit invalid\r\n");
    printf("        limit = x.xx\r\n");
    printf("\r\n");
    
    printf("实际测试输出：\r\n");
    
    // 设置一个已知值
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("25.5");
    
    printf("\r\n正常情况测试：\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("50.12");
    
    printf("\r\n错误情况测试：\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("510.12");
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*!
    \brief      完整的阈值功能测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void limit_complete_function_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        阈值设置功能完整测试          #\r\n");
    printf("##########################################\r\n");
    
    // 1. 显示测试说明
    test_limit_complete_flow();
    
    // 2. 正常设置测试
    test_normal_limit_setting();
    
    // 3. 边界值测试
    test_limit_boundary_values();
    
    // 4. 错误输入测试
    test_limit_invalid_inputs();
    
    // 5. 浮点数精度测试
    test_limit_float_precision();
    
    // 6. 输出格式验证
    verify_limit_output_format();
    
    printf("\r\n##########################################\r\n");
    printf("#        测试完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n现在可以通过串口手动测试：\r\n");
    printf("1. 发送 'limit' 命令\r\n");
    printf("2. 输入阈值 (0-500)\r\n");
    printf("3. 观察输出格式是否正确\r\n");
    printf("4. 测试各种边界值和错误输入\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行测试：
 * 
 * 1. limit_complete_function_test();  // 完整功能测试
 * 2. test_normal_limit_setting();     // 正常设置测试
 * 3. test_limit_invalid_inputs();     // 错误输入测试
 * 4. verify_limit_output_format();    // 格式验证
 * 
 * 然后通过串口发送以下命令进行实际测试：
 * - "limit" -> 应该显示当前阈值和输入提示
 * - "50.12" -> 应该返回成功消息和新阈值
 * - "510.12" -> 应该返回错误消息和当前阈值
 * 
 * 期望的交互完全符合题目要求：
 * 输入: limit
 * 输出: limit = 1.00
 *       Input value(0~500):
 * 
 * 输入: 50.12
 * 输出: limit modified success
 *       limit = 50.12
 * 
 * 输入: limit
 * 输出: limit = 50.12
 *       Input value(0~500):
 * 
 * 输入: 510.12
 * 输出: limit invalid
 *       limit = 50.12
 */
