/*
 * config.ini文件读取器实现
 * 支持从SD卡读取和解析config.ini配置文件
 */

#include "mcu_cmic_gd32f470vet6.h"
#include "config_reader.h"
#include <string.h>
#include <stdlib.h>

// 全局配置数据
static config_data_t g_config_data = {0};

// 外部FatFS变量
extern FATFS fs;

/*!
    \brief      去除字符串首尾空白字符
    \param[in]  str: 输入字符串
    \param[out] none
    \retval     处理后的字符串指针
*/
static char* trim_whitespace(char* str)
{
    char* end;
    
    // 去除前导空白
    while (*str == ' ' || *str == '\t' || *str == '\r' || *str == '\n') {
        str++;
    }
    
    if (*str == 0) {
        return str;
    }
    
    // 去除尾部空白
    end = str + strlen(str) - 1;
    while (end > str && (*end == ' ' || *end == '\t' || *end == '\r' || *end == '\n')) {
        end--;
    }
    
    // 添加字符串结束符
    *(end + 1) = '\0';
    
    return str;
}

/*!
    \brief      解析配置文件行
    \param[in]  line: 配置文件行
    \param[in]  current_section: 当前节名
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
static int parse_config_line(char* line, char* current_section)
{
    char* trimmed_line;
    char* key;
    char* value;
    char* equals_pos;
    
    if (!line || g_config_data.item_count >= CONFIG_MAX_ITEMS) {
        return -1;
    }
    
    // 去除空白字符
    trimmed_line = trim_whitespace(line);
    
    // 跳过空行和注释行
    if (strlen(trimmed_line) == 0 || trimmed_line[0] == ';' || trimmed_line[0] == '#') {
        return 0;
    }
    
    // 检查是否是节标题 [section]
    if (trimmed_line[0] == '[') {
        char* close_bracket = strchr(trimmed_line, ']');
        if (close_bracket) {
            *close_bracket = '\0';
            strncpy(current_section, trimmed_line + 1, CONFIG_MAX_SECTION_LENGTH - 1);
            current_section[CONFIG_MAX_SECTION_LENGTH - 1] = '\0';
            return 0;
        }
    }
    
    // 查找等号分隔符
    equals_pos = strchr(trimmed_line, '=');
    if (!equals_pos) {
        return 0; // 不是有效的键值对
    }
    
    // 分离键和值
    *equals_pos = '\0';
    key = trim_whitespace(trimmed_line);
    value = trim_whitespace(equals_pos + 1);
    
    // 存储配置项
    config_item_t* item = &g_config_data.items[g_config_data.item_count];
    strncpy(item->section, current_section, CONFIG_MAX_SECTION_LENGTH - 1);
    item->section[CONFIG_MAX_SECTION_LENGTH - 1] = '\0';
    strncpy(item->key, key, CONFIG_MAX_KEY_LENGTH - 1);
    item->key[CONFIG_MAX_KEY_LENGTH - 1] = '\0';
    strncpy(item->value, value, CONFIG_MAX_VALUE_LENGTH - 1);
    item->value[CONFIG_MAX_VALUE_LENGTH - 1] = '\0';
    
    g_config_data.item_count++;
    
    return 0;
}

/*!
    \brief      从SD卡加载配置文件
    \param[in]  filename: 配置文件名
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int config_load_from_sd(const char* filename)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_read;
    char line_buffer[CONFIG_MAX_LINE_LENGTH];
    char current_section[CONFIG_MAX_SECTION_LENGTH] = "";
    char file_path[64];
    int line_pos = 0;
    char ch;
    
    // 清空之前的配置数据
    config_clear();
    
    // 构建完整文件路径
    snprintf(file_path, sizeof(file_path), "0:/%s", filename);
    
    my_printf(DEBUG_USART, "尝试打开配置文件: %s\r\n", file_path);
    
    // 打开配置文件
    result = f_open(&config_file, file_path, FA_READ);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "无法打开配置文件: %s (错误码: %d)\r\n", file_path, result);
        return -1;
    }
    
    my_printf(DEBUG_USART, "配置文件打开成功，开始解析...\r\n");
    
    // 逐字符读取文件，按行解析
    while (f_read(&config_file, &ch, 1, &bytes_read) == FR_OK && bytes_read > 0) {
        if (ch == '\n' || ch == '\r') {
            if (line_pos > 0) {
                line_buffer[line_pos] = '\0';
                parse_config_line(line_buffer, current_section);
                line_pos = 0;
            }
        } else if (line_pos < CONFIG_MAX_LINE_LENGTH - 1) {
            line_buffer[line_pos++] = ch;
        }
    }
    
    // 处理最后一行（如果文件不以换行符结尾）
    if (line_pos > 0) {
        line_buffer[line_pos] = '\0';
        parse_config_line(line_buffer, current_section);
    }
    
    f_close(&config_file);
    
    g_config_data.is_loaded = true;
    my_printf(DEBUG_USART, "配置文件解析完成，共读取 %d 个配置项\r\n", g_config_data.item_count);
    
    return 0;
}

/*!
    \brief      通用配置文件加载函数
    \param[in]  filename: 配置文件名
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int config_load_from_file(const char* filename)
{
    return config_load_from_sd(filename);
}

/*!
    \brief      获取字符串配置值
    \param[in]  section: 节名
    \param[in]  key: 键名
    \param[in]  default_value: 默认值
    \param[out] none
    \retval     配置值字符串
*/
const char* config_get_string(const char* section, const char* key, const char* default_value)
{
    for (uint16_t i = 0; i < g_config_data.item_count; i++) {
        if (strcmp(g_config_data.items[i].section, section) == 0 &&
            strcmp(g_config_data.items[i].key, key) == 0) {
            return g_config_data.items[i].value;
        }
    }
    return default_value;
}

/*!
    \brief      获取整数配置值
    \param[in]  section: 节名
    \param[in]  key: 键名
    \param[in]  default_value: 默认值
    \param[out] none
    \retval     配置值整数
*/
int config_get_int(const char* section, const char* key, int default_value)
{
    const char* value_str = config_get_string(section, key, NULL);
    if (value_str) {
        return atoi(value_str);
    }
    return default_value;
}

/*!
    \brief      获取浮点数配置值
    \param[in]  section: 节名
    \param[in]  key: 键名
    \param[in]  default_value: 默认值
    \param[out] none
    \retval     配置值浮点数
*/
float config_get_float(const char* section, const char* key, float default_value)
{
    const char* value_str = config_get_string(section, key, NULL);
    if (value_str) {
        return atof(value_str);
    }
    return default_value;
}

/*!
    \brief      获取布尔配置值
    \param[in]  section: 节名
    \param[in]  key: 键名
    \param[in]  default_value: 默认值
    \param[out] none
    \retval     配置值布尔
*/
bool config_get_bool(const char* section, const char* key, bool default_value)
{
    const char* value_str = config_get_string(section, key, NULL);
    if (value_str) {
        if (strcmp(value_str, "true") == 0 || strcmp(value_str, "1") == 0 || 
            strcmp(value_str, "yes") == 0 || strcmp(value_str, "on") == 0) {
            return true;
        } else if (strcmp(value_str, "false") == 0 || strcmp(value_str, "0") == 0 || 
                   strcmp(value_str, "no") == 0 || strcmp(value_str, "off") == 0) {
            return false;
        }
    }
    return default_value;
}

/*!
    \brief      打印所有配置项
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_print_all(void)
{
    my_printf(DEBUG_USART, "\r\n=== 配置文件内容 ===\r\n");
    my_printf(DEBUG_USART, "配置项数量: %d\r\n", g_config_data.item_count);
    my_printf(DEBUG_USART, "加载状态: %s\r\n", g_config_data.is_loaded ? "已加载" : "未加载");
    
    for (uint16_t i = 0; i < g_config_data.item_count; i++) {
        my_printf(DEBUG_USART, "[%s] %s = %s\r\n", 
                  g_config_data.items[i].section,
                  g_config_data.items[i].key,
                  g_config_data.items[i].value);
    }
    my_printf(DEBUG_USART, "==================\r\n");
}

/*!
    \brief      清空配置数据
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_clear(void)
{
    memset(&g_config_data, 0, sizeof(g_config_data));
}

/*!
    \brief      获取配置项数量
    \param[in]  none
    \param[out] none
    \retval     配置项数量
*/
int config_get_item_count(void)
{
    return g_config_data.item_count;
}

/*!
    \brief      检查配置是否已加载
    \param[in]  none
    \param[out] none
    \retval     true: 已加载, false: 未加载
*/
bool config_is_loaded(void)
{
    return g_config_data.is_loaded;
}

/*!
    \brief      创建示例配置文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_create_sample_file(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;

    const char* sample_config =
        "; 这是一个示例配置文件\r\n"
        "; 注释行以分号开头\r\n"
        "\r\n"
        "[system]\r\n"
        "device_name = CIMC-Device\r\n"
        "version = 1.0.0\r\n"
        "debug_mode = true\r\n"
        "max_users = 100\r\n"
        "\r\n"
        "[network]\r\n"
        "ip_address = *************\r\n"
        "port = 8080\r\n"
        "timeout = 30.5\r\n"
        "enable_dhcp = false\r\n"
        "\r\n"
        "[sensor]\r\n"
        "sample_rate = 1000\r\n"
        "calibration_factor = 1.25\r\n"
        "enable_filter = true\r\n"
        "threshold = 50.0\r\n";

    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        result = f_write(&config_file, sample_config, strlen(sample_config), &bytes_written);
        f_close(&config_file);

        if (result == FR_OK) {
            my_printf(DEBUG_USART, "示例配置文件创建成功: config.ini\r\n");
        } else {
            my_printf(DEBUG_USART, "写入配置文件失败\r\n");
        }
    } else {
        my_printf(DEBUG_USART, "创建配置文件失败\r\n");
    }
}

/*!
    \brief      测试配置文件解析器
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_test_parser(void)
{
    my_printf(DEBUG_USART, "\r\n=== 配置文件解析器测试 ===\r\n");

    // 1. 创建示例配置文件
    my_printf(DEBUG_USART, "1. 创建示例配置文件...\r\n");
    config_create_sample_file();

    // 2. 加载配置文件
    my_printf(DEBUG_USART, "2. 加载配置文件...\r\n");
    if (config_load_from_file("config.ini") == 0) {
        my_printf(DEBUG_USART, "配置文件加载成功\r\n");
    } else {
        my_printf(DEBUG_USART, "配置文件加载失败\r\n");
        return;
    }

    // 3. 打印所有配置项
    my_printf(DEBUG_USART, "3. 显示所有配置项...\r\n");
    config_print_all();

    // 4. 测试各种数据类型读取
    my_printf(DEBUG_USART, "4. 测试数据类型读取...\r\n");

    // 字符串测试
    const char* device_name = config_get_string("system", "device_name", "Unknown");
    my_printf(DEBUG_USART, "设备名称: %s\r\n", device_name);

    // 整数测试
    int max_users = config_get_int("system", "max_users", 0);
    my_printf(DEBUG_USART, "最大用户数: %d\r\n", max_users);

    // 浮点数测试
    float timeout = config_get_float("network", "timeout", 0.0f);
    my_printf(DEBUG_USART, "超时时间: %.1f\r\n", timeout);

    // 布尔值测试
    bool debug_mode = config_get_bool("system", "debug_mode", false);
    my_printf(DEBUG_USART, "调试模式: %s\r\n", debug_mode ? "开启" : "关闭");

    // 测试不存在的配置项
    const char* unknown = config_get_string("unknown", "key", "默认值");
    my_printf(DEBUG_USART, "不存在的配置项: %s\r\n", unknown);

    my_printf(DEBUG_USART, "=== 测试完成 ===\r\n");
}
