/*
 * 周期采样模式和周期调整功能测试
 * 基于宝宝的底层框架实现
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试start命令启动周期采样
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_start_command(void)
{
    printf("\r\n=== 测试start命令 ===\r\n");
    
    printf("发送命令: start\r\n");
    printf("期望结果:\r\n");
    printf("1. uart_flag=1，启动周期采样模式\r\n");
    printf("2. LED1开始闪烁（1秒周期）\r\n");
    printf("3. 每5秒输出一条采样数据\r\n");
    printf("4. 格式: YYYY-MM-DD HH:MM:SS ch0=xx.xxV\r\n");
    printf("5. 电压值 = ADC电压(0-3.3V) × ratio\r\n");
    printf("\r\n");
    
    printf("实际执行:\r\n");
    process_uart_command("start");
    
    printf("uart_flag状态: %d (1=启动)\r\n", uart_flag);
    printf("采样周期: %d秒\r\n", get_sample_cycle());
    
    printf("\r\n=== start命令测试完成 ===\r\n");
}

/*!
    \brief      测试按键周期调整功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_key_cycle_adjustment(void)
{
    printf("\r\n=== 测试按键周期调整 ===\r\n");
    
    printf("按键功能说明:\r\n");
    printf("KEY1: 启停控制（切换uart_flag）\r\n");
    printf("KEY2: 设置5s采样周期\r\n");
    printf("KEY3: 设置10s采样周期\r\n");
    printf("KEY4: 设置15s采样周期\r\n");
    printf("\r\n");
    
    printf("模拟按键测试:\r\n");
    
    // 模拟按KEY2（5秒周期）
    printf("1. 模拟按KEY2（5秒周期）:\r\n");
    printf("期望输出: sample cycle adjust: 5s\r\n");
    printf("实际输出: ");
    set_sample_cycle(5);
    printf("\r\n");
    
    // 模拟按KEY3（10秒周期）
    printf("2. 模拟按KEY3（10秒周期）:\r\n");
    printf("期望输出: sample cycle adjust: 10s\r\n");
    printf("实际输出: ");
    set_sample_cycle(10);
    printf("\r\n");
    
    // 模拟按KEY4（15秒周期）
    printf("3. 模拟按KEY4（15秒周期）:\r\n");
    printf("期望输出: sample cycle adjust: 15s\r\n");
    printf("实际输出: ");
    set_sample_cycle(15);
    printf("\r\n");
    
    printf("=== 按键周期调整测试完成 ===\r\n");
}

/*!
    \brief      测试配置持久化功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_config_persistence(void)
{
    printf("\r\n=== 测试配置持久化 ===\r\n");
    
    printf("1. 保存不同周期到Flash:\r\n");
    
    // 保存5秒周期
    save_sample_cycle_to_flash(5);
    uint32_t loaded_cycle = load_sample_cycle_from_flash();
    printf("保存5s，读取: %ds %s\r\n", loaded_cycle, (loaded_cycle == 5) ? "✓" : "✗");
    
    // 保存10秒周期
    save_sample_cycle_to_flash(10);
    loaded_cycle = load_sample_cycle_from_flash();
    printf("保存10s，读取: %ds %s\r\n", loaded_cycle, (loaded_cycle == 10) ? "✓" : "✗");
    
    // 保存15秒周期
    save_sample_cycle_to_flash(15);
    loaded_cycle = load_sample_cycle_from_flash();
    printf("保存15s，读取: %ds %s\r\n", loaded_cycle, (loaded_cycle == 15) ? "✓" : "✗");
    
    printf("\r\n2. 验证断电重启后配置生效:\r\n");
    printf("系统启动时会自动调用load_sample_cycle_from_flash()\r\n");
    printf("当前加载的周期: %ds\r\n", get_sample_cycle());
    
    printf("\r\n=== 配置持久化测试完成 ===\r\n");
}

/*!
    \brief      演示完整的采样流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void demo_complete_sampling_flow(void)
{
    printf("\r\n=== 完整采样流程演示 ===\r\n");
    
    printf("场景：用户通过按键调整周期并启动采样\r\n");
    printf("\r\n");
    
    printf("步骤1: 按KEY3设置10秒周期\r\n");
    set_sample_cycle(10);
    printf("\r\n");
    
    printf("步骤2: 发送start命令启动采样\r\n");
    process_uart_command("start");
    printf("采样状态: %s\r\n", uart_flag ? "已启动" : "未启动");
    printf("采样周期: %ds\r\n", get_sample_cycle());
    printf("\r\n");
    
    printf("步骤3: 模拟采样输出（每10秒一次）\r\n");
    printf("期望格式: 2025-01-01 00:30:05 ch0=10.50V\r\n");
    printf("          2025-01-01 00:30:15 ch0=10.50V\r\n");
    printf("\r\n");
    
    printf("步骤4: 在采样过程中按KEY4调整为15秒周期\r\n");
    set_sample_cycle(15);
    printf("新的采样间隔: %ds\r\n", get_sample_cycle());
    printf("\r\n");
    
    printf("✅ 功能特点:\r\n");
    printf("1. 基于您的uart_flag=1启动机制\r\n");
    printf("2. ADC电压(0-3.3V) × ratio = 实际电压\r\n");
    printf("3. 按键动态调整周期(5s/10s/15s)\r\n");
    printf("4. 配置持久化，断电重启后生效\r\n");
    printf("5. RTC时间戳精确控制\r\n");
    
    printf("\r\n=== 演示完成 ===\r\n");
}

/*!
    \brief      验证电压计算功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_voltage_calculation(void)
{
    printf("\r\n=== 验证电压计算 ===\r\n");
    
    printf("电压计算公式: 实际电压 = ADC电压(0-3.3V) × ratio\r\n");
    printf("\r\n");
    
    // 设置不同的ratio值进行测试
    float test_ratios[] = {1.0f, 2.0f, 5.0f, 10.0f};
    int num_tests = sizeof(test_ratios) / sizeof(test_ratios[0]);
    
    for (int i = 0; i < num_tests; i++) {
        printf("测试ratio = %.1f:\r\n", test_ratios[i]);
        
        // 模拟设置ratio
        set_current_ratio(test_ratios[i]);
        
        // 获取几个电压样本
        for (int j = 0; j < 3; j++) {
            float adc_voltage = get_channel_voltage(0);  // 0-3.3V
            float actual_voltage = adc_voltage * test_ratios[i];
            printf("  ADC: %.2fV → 实际: %.2fV\r\n", adc_voltage, actual_voltage);
        }
        printf("\r\n");
    }
    
    printf("=== 电压计算验证完成 ===\r\n");
}

/*!
    \brief      完整功能测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_sampling_cycle_complete(void)
{
    printf("\r\n========================================\r\n");
    printf("周期采样模式和周期调整功能完整测试\r\n");
    printf("========================================\r\n");
    
    printf("📋 功能要求:\r\n");
    printf("1. start命令启动周期采样模式\r\n");
    printf("2. uart_flag=1表示启动状态\r\n");
    printf("3. ADC电压(0-3.3V) × ratio = 实际电压\r\n");
    printf("4. KEY2/KEY3/KEY4调整周期(5s/10s/15s)\r\n");
    printf("5. 配置持久化，断电重启后生效\r\n");
    printf("\r\n");
    
    // 执行各项测试
    test_start_command();
    test_key_cycle_adjustment();
    test_config_persistence();
    test_voltage_calculation();
    demo_complete_sampling_flow();
    
    printf("\r\n========================================\r\n");
    printf("✅ 所有功能测试完成！\r\n");
    printf("现在可以通过以下方式测试:\r\n");
    printf("1. 串口发送 'start' 启动采样\r\n");
    printf("2. 按KEY2/KEY3/KEY4调整周期\r\n");
    printf("3. 按KEY1控制启停\r\n");
    printf("4. 观察串口输出的采样数据\r\n");
    printf("========================================\r\n");
}
