#ifndef __RTC_APP_H_
#define __RTC_APP_H_

#include "stdint.h"
#include "gd32f4xx.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /* RTC基础功能函数 */
    void rtc_basic_init(void); // RTC基础初始化
    void rtc_task(void);       // RTC任务显示

    /* RTC时间设置函数 */
    void rtc_time_set(const char *time_str);                   // RTC时间设置功能
    void rtc_handle_time_commands(uint8_t *command_buffer);    // 处理RTC时间设置命令
    void rtc_handle_config_command(void);                      // 处理RTC Config命令
    uint8_t rtc_process_datetime_input(uint8_t *input_buffer); // 处理日期时间输入
    uint8_t rtc_is_waiting_datetime_input(void);               // 检查是否等待日期时间输入

    /* 时区处理函数 */
    void rtc_set_timezone_offset(int8_t offset);                                                            // 设置时区偏移
    int8_t rtc_get_timezone_offset(void);                                                                   // 获取时区偏移
    void rtc_apply_timezone_offset(rtc_parameter_struct *time_param, rtc_parameter_struct *adjusted_param); // 应用时区偏移

#ifdef __cplusplus
}
#endif

#endif /* __RTC_APP_H_ */
