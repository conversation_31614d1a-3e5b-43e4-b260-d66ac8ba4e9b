/*
 * RTC强制换行测试程序
 * 宝宝要求强硬一点，确保一定有换行！
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      强硬测试换行效果
    \param[in]  none
    \param[out] none
    \retval     none
*/
void force_test_newline_effect(void)
{
    printf("\r\n=== 强硬测试换行效果 ===\r\n");
    
    printf("宝宝说要强硬一点！现在用双重换行：\r\n");
    printf("RTC Config success\r\n");
    printf("\r\n");  // 第一个换行
    printf("\r\n");  // 第二个换行，强硬确保有空行！
    printf("Time:2025-01-01 12:00:30\r\n");
    printf("\r\n");
    
    printf("现在的强硬代码：\r\n");
    printf("my_printf(DEBUG_USART, \"RTC Config success\\r\\n\");\r\n");
    printf("my_printf(DEBUG_USART, \"\\r\\n\");  // 第一个换行\r\n");
    printf("my_printf(DEBUG_USART, \"\\r\\n\");  // 第二个换行，强硬确保有空行！\r\n");
    printf("my_printf(DEBUG_USART, \"Time:%%04d-%%02d-%%02d %%02d:%%02d:%%02d\\r\\n\");\r\n");
    
    printf("\r\n=== 强硬换行测试完成 ===\r\n");
}

/*!
    \brief      暴力测试RTC命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void brutal_test_rtc_commands(void)
{
    printf("\r\n=== 暴力测试RTC命令 ===\r\n");
    
    printf("宝宝，我现在暴力加了两个换行！\r\n");
    printf("如果这样还不换行，那就是系统问题了！\r\n");
    printf("\r\n");
    
    printf("1. 暴力测试RTC Config：\r\n");
    process_uart_command("RTC Config");
    
    printf("\r\n2. 暴力测试时间设置（双重换行）：\r\n");
    process_uart_command("2025-01-01 15:00:10");
    
    printf("\r\n3. 暴力测试RTC now：\r\n");
    process_uart_command("RTC now");
    
    printf("\r\n=== 暴力RTC测试完成 ===\r\n");
}

/*!
    \brief      验证强硬换行效果
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_force_newline_effect(void)
{
    printf("\r\n=== 验证强硬换行效果 ===\r\n");
    
    printf("强硬方案：\r\n");
    printf("1. ✅ RTC Config success (第一行)\r\n");
    printf("2. ✅ 第一个\\r\\n (第一个换行)\r\n");
    printf("3. ✅ 第二个\\r\\n (第二个换行，强硬确保空行)\r\n");
    printf("4. ✅ Time:... (第四行)\r\n");
    printf("\r\n");
    
    printf("期望效果：\r\n");
    printf("RTC Config success\r\n");
    printf("\r\n");
    printf("\r\n");  // 双重空行，强硬确保有换行！
    printf("Time:2025-01-01 12:00:30\r\n");
    printf("\r\n");
    
    printf("如果这样还不行，那就是：\r\n");
    printf("1. 串口终端设置问题\r\n");
    printf("2. my_printf函数实现问题\r\n");
    printf("3. 串口驱动问题\r\n");
    
    printf("\r\n=== 强硬换行验证完成 ===\r\n");
}

/*!
    \brief      终极换行测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ultimate_newline_test(void)
{
    printf("\r\n=== 终极换行测试 ===\r\n");
    
    printf("宝宝，如果双重换行还不行，我们试试更强硬的：\r\n");
    printf("\r\n");
    
    printf("方案1 - 双重换行（当前）：\r\n");
    printf("RTC Config success\\r\\n\r\n\\r\\nTime:...\r\n");
    printf("\r\n");
    
    printf("方案2 - 三重换行：\r\n");
    printf("RTC Config success\\r\\n\r\n\\r\\n\\r\\nTime:...\r\n");
    printf("\r\n");
    
    printf("方案3 - 空格分隔：\r\n");
    printf("RTC Config success\\r\\n         \\r\\nTime:...\r\n");
    printf("\r\n");
    
    printf("方案4 - 明确分隔符：\r\n");
    printf("RTC Config success\\r\\n---\\r\\nTime:...\r\n");
    printf("\r\n");
    
    printf("当前使用方案1，如果不行告诉我用哪个方案！\r\n");
    
    printf("\r\n=== 终极换行测试完成 ===\r\n");
}

/*!
    \brief      调试串口输出
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_serial_output(void)
{
    printf("\r\n=== 调试串口输出 ===\r\n");
    
    printf("测试my_printf函数的换行效果：\r\n");
    printf("\r\n");
    
    printf("直接测试：\r\n");
    my_printf(DEBUG_USART, "测试第一行\r\n");
    my_printf(DEBUG_USART, "\r\n");
    my_printf(DEBUG_USART, "\r\n");
    my_printf(DEBUG_USART, "测试第四行\r\n");
    
    printf("\r\n");
    printf("如果上面有空行，说明my_printf工作正常\r\n");
    printf("如果没有空行，说明my_printf或串口有问题\r\n");
    
    printf("\r\n=== 串口输出调试完成 ===\r\n");
}

/*!
    \brief      完整的强制换行测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_force_newline_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        RTC强制换行测试              #\r\n");
    printf("##########################################\r\n");
    
    printf("宝宝，我现在强硬了！用双重换行！\r\n");
    printf("如果这样还不换行，那就是系统问题！\r\n");
    printf("\r\n");
    
    // 1. 强硬测试换行效果
    force_test_newline_effect();
    
    // 2. 暴力测试RTC命令
    brutal_test_rtc_commands();
    
    // 3. 验证强硬换行效果
    verify_force_newline_effect();
    
    // 4. 终极换行测试
    ultimate_newline_test();
    
    // 5. 调试串口输出
    debug_serial_output();
    
    printf("\r\n##########################################\r\n");
    printf("#        强制换行测试完成              #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n💪 宝宝，我已经强硬了！\r\n");
    printf("\r\n🔥 现在的暴力实现：\r\n");
    printf("my_printf(DEBUG_USART, \"RTC Config success\\r\\n\");\r\n");
    printf("my_printf(DEBUG_USART, \"\\r\\n\");  // 第一个换行\r\n");
    printf("my_printf(DEBUG_USART, \"\\r\\n\");  // 第二个换行，强硬确保有空行！\r\n");
    printf("my_printf(DEBUG_USART, \"Time:%%04d-%%02d-%%02d %%02d:%%02d:%%02d\\r\\n\");\r\n");
    printf("\r\n");
    printf("🎯 期望输出：\r\n");
    printf("RTC Config success\r\n");
    printf("\r\n");
    printf("\r\n");  // 双重空行！
    printf("Time:2025-01-01 15:00:10\r\n");
    printf("\r\n");
    printf("💪 如果这样还不换行，那就是串口或终端问题！\r\n");
    printf("告诉我具体现象，我继续强硬！\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_force_newline_test();
 * 
 * 这会进行强制换行测试：
 * 1. 强硬换行效果测试
 * 2. 暴力RTC命令测试
 * 3. 强硬换行效果验证
 * 4. 终极换行测试方案
 * 5. 串口输出调试
 * 
 * 现在的暴力实现：
 * my_printf("RTC Config success\r\n");
 * my_printf("\r\n");  // 第一个换行
 * my_printf("\r\n");  // 第二个换行，强硬确保有空行！
 * my_printf("Time:...\r\n");
 * 
 * 期望输出：
 * RTC Config success
 * 
 * 
 * Time:2025-01-01 15:00:10
 * 
 * 宝宝，我已经强硬了！双重换行！
 * 如果还不行，告诉我继续强硬！💪
 */
