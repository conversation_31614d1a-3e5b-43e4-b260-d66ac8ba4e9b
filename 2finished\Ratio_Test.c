/*
 * 变比设置功能测试程序
 * 验证是否完全符合题目要求
 */

#include "mcu_cmic_gd32f470vet6.h"

// 测试变比设置功能的完整流程
void test_ratio_complete_flow(void)
{
    printf("\r\n========================================\r\n");
    printf("         变比设置功能测试\r\n");
    printf("========================================\r\n");
    
    printf("题目要求：\r\n");
    printf("输入指令\"ratio\"，首先读取出原有的变比值，然后提示输入新的变比\r\n");
    printf("有效值范围为 0-100，变量类型为浮点数\r\n");
    printf("\r\n");
    
    printf("测试步骤：\r\n");
    printf("1. 发送: ratio\r\n");
    printf("   期望: ratio=1.0\r\n");
    printf("         Input value(0~100):\r\n");
    printf("\r\n");
    printf("2. 发送: 10.5\r\n");
    printf("   期望: ratio modified success ratio=10.5\r\n");
    printf("\r\n");
    printf("3. 发送: ratio\r\n");
    printf("   期望: ratio=10.5\r\n");
    printf("         Input value(0~100):\r\n");
    printf("\r\n");
    printf("4. 发送: 100.5 (超出范围)\r\n");
    printf("   期望: ratio invalid\r\n");
    printf("         ratio=10.5\r\n");
    printf("\r\n");
    printf("5. 发送: -5 (负值)\r\n");
    printf("   期望: ratio invalid\r\n");
    printf("         ratio=10.5\r\n");
    printf("\r\n");
    printf("6. 发送: abc (非数字)\r\n");
    printf("   期望: ratio invalid\r\n");
    printf("         ratio=10.5\r\n");
    printf("\r\n");
    printf("========================================\r\n");
}

// 手动测试变比设置功能
void manual_test_ratio_setting(void)
{
    printf("\r\n=== 手动变比设置测试 ===\r\n");
    
    // 测试1：正常设置
    printf("测试1：正常设置变比为10.5\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);  // 短暂延时
    process_uart_command("10.5");
    
    printf("\r\n");
    
    // 测试2：查看当前变比
    printf("测试2：查看当前变比\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("25.0");  // 设置为25.0
    
    printf("\r\n");
    
    // 测试3：超出范围测试
    printf("测试3：超出范围测试 (100.5)\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("100.5");
    
    printf("\r\n");
    
    // 测试4：负值测试
    printf("测试4：负值测试 (-5.0)\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("-5.0");
    
    printf("\r\n");
    
    // 测试5：非数字测试
    printf("测试5：非数字测试 (abc)\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("abc");
    
    printf("\r\n");
    
    // 测试6：边界值测试
    printf("测试6：边界值测试 (0.0 和 100.0)\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("0.0");
    
    printf("设置为100.0:\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("100.0");
    
    printf("===================\r\n");
}

// 验证变比值存储和获取
void test_ratio_storage(void)
{
    printf("\r\n=== 变比值存储测试 ===\r\n");
    
    float current = get_current_ratio();
    printf("当前变比值: %.1f\r\n", current);
    
    // 设置几个不同的值并验证
    printf("设置变比为15.5:\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("15.5");
    
    current = get_current_ratio();
    printf("验证变比值: %.1f\r\n", current);
    
    if (current == 15.5f) {
        printf("✓ 变比值存储正确\r\n");
    } else {
        printf("✗ 变比值存储错误\r\n");
    }
    
    printf("==================\r\n");
}

// 测试浮点数解析精度
void test_float_parsing(void)
{
    printf("\r\n=== 浮点数解析测试 ===\r\n");
    
    // 测试各种浮点数格式
    const char* test_values[] = {
        "1.0",      // 简单小数
        "10.5",     // 一位小数
        "99.99",    // 两位小数
        "0.1",      // 小于1的数
        "50",       // 整数
        "0.0",      // 零
        "100.0",    // 边界值
        "50.123",   // 多位小数
        NULL
    };
    
    for (int i = 0; test_values[i] != NULL; i++) {
        printf("测试值: %s\r\n", test_values[i]);
        process_uart_command("ratio");
        for(volatile int j = 0; j < 300000; j++);
        process_uart_command((char*)test_values[i]);
        printf("\r\n");
    }
    
    printf("==================\r\n");
}

// 错误输入测试
void test_error_inputs(void)
{
    printf("\r\n=== 错误输入测试 ===\r\n");
    
    const char* error_values[] = {
        "101.0",    // 超出上限
        "-1.0",     // 负值
        "abc",      // 非数字
        "12.34.56", // 多个小数点
        "",         // 空字符串
        "1e5",      // 科学计数法
        "inf",      // 无穷大
        "nan",      // 非数字
        NULL
    };
    
    for (int i = 0; error_values[i] != NULL; i++) {
        printf("错误输入测试: %s\r\n", error_values[i]);
        process_uart_command("ratio");
        for(volatile int j = 0; j < 300000; j++);
        process_uart_command((char*)error_values[i]);
        printf("\r\n");
    }
    
    printf("==================\r\n");
}

// 完整的变比功能测试
void ratio_complete_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#          变比功能完整测试             #\r\n");
    printf("##########################################\r\n");
    
    // 1. 显示测试说明
    test_ratio_complete_flow();
    
    // 2. 手动测试
    manual_test_ratio_setting();
    
    // 3. 存储测试
    test_ratio_storage();
    
    // 4. 浮点数解析测试
    test_float_parsing();
    
    // 5. 错误输入测试
    test_error_inputs();
    
    printf("\r\n##########################################\r\n");
    printf("#          测试完成                    #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n现在可以通过串口手动测试：\r\n");
    printf("1. 发送 'ratio' 命令\r\n");
    printf("2. 输入变比值 (0-100)\r\n");
    printf("3. 观察输出格式是否正确\r\n");
}

// 快速验证宏
#define RATIO_QUICK_TEST() do { \
    printf("\r\n=== 变比快速测试 ===\r\n"); \
    printf("当前变比: %.1f\r\n", get_current_ratio()); \
    printf("请通过串口发送以下命令测试：\r\n"); \
    printf("1. ratio\r\n"); \
    printf("2. 10.5\r\n"); \
    printf("3. ratio\r\n"); \
    printf("4. 100.5 (应该失败)\r\n"); \
    printf("==================\r\n"); \
} while(0)

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行测试：
 * 
 * 1. ratio_complete_test();     // 完整测试
 * 2. RATIO_QUICK_TEST();        // 快速测试
 * 
 * 然后通过串口发送以下命令进行实际测试：
 * - "ratio" -> 应该显示当前变比值和输入提示
 * - "10.5" -> 应该返回 "ratio modified success ratio=10.5"
 * - "100.5" -> 应该返回 "ratio invalid" 和当前变比值
 */
