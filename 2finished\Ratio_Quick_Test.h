/*
 * 变比功能快速测试头文件
 * 提供简单的测试接口
 */

#ifndef __RATIO_QUICK_TEST_H__
#define __RATIO_QUICK_TEST_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 测试函数声明
void test_ratio_complete_flow(void);
void manual_test_ratio_setting(void);
void test_ratio_storage(void);
void test_float_parsing(void);
void test_error_inputs(void);
void ratio_complete_test(void);

// 快速测试宏
#define RATIO_QUICK_TEST() do { \
    printf("\r\n=== 变比快速测试 ===\r\n"); \
    printf("当前变比: %.1f\r\n", get_current_ratio()); \
    printf("请通过串口发送以下命令测试：\r\n"); \
    printf("1. ratio -> 应显示当前值和输入提示\r\n"); \
    printf("2. 10.5 -> 应显示成功消息\r\n"); \
    printf("3. ratio -> 应显示更新后的值\r\n"); \
    printf("4. 100.5 -> 应显示错误消息\r\n"); \
    printf("5. -5 -> 应显示错误消息\r\n"); \
    printf("6. abc -> 应显示错误消息\r\n"); \
    printf("==================\r\n"); \
} while(0)

// 验证输出格式宏
#define VERIFY_RATIO_FORMAT() do { \
    printf("\r\n=== 验证输出格式 ===\r\n"); \
    printf("期望格式：\r\n"); \
    printf("ratio=1.0\r\n"); \
    printf("Input value(0~100):\r\n"); \
    printf("ratio modified success ratio=10.5\r\n"); \
    printf("ratio invalid\r\n"); \
    printf("ratio=10.5\r\n"); \
    printf("==================\r\n"); \
} while(0)

#ifdef __cplusplus
}
#endif

#endif /* __RATIO_QUICK_TEST_H__ */
