/*
 * 软件RTC解决方案 - 确保RTC时钟能够正常运行
 * 宝宝，这个方案使用系统滴答定时器来模拟RTC运行！
 */

#include "mcu_cmic_gd32f470vet6.h"

// 软件RTC时间结构
typedef struct {
    int year;
    int month;
    int day;
    int hour;
    int minute;
    int second;
    uint32_t last_tick;  // 上次更新的系统滴答
} software_rtc_t;

// 全局软件RTC变量
static software_rtc_t g_soft_rtc = {
    .year = 2025,
    .month = 1,
    .day = 1,
    .hour = 12,
    .minute = 0,
    .second = 0,
    .last_tick = 0
};

// 月份天数表（平年）
static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

/*!
    \brief      判断是否为闰年
    \param[in]  year: 年份
    \param[out] none
    \retval     int: 1=闰年, 0=平年
*/
int is_leap_year(int year)
{
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

/*!
    \brief      获取指定月份的天数
    \param[in]  year: 年份
    \param[in]  month: 月份 (1-12)
    \param[out] none
    \retval     int: 天数
*/
int get_days_in_month(int year, int month)
{
    if (month == 2 && is_leap_year(year)) {
        return 29;
    }
    return days_in_month[month - 1];
}

/*!
    \brief      软件RTC时间更新
    \param[in]  none
    \param[out] none
    \retval     none
*/
void software_rtc_update(void)
{
    uint32_t current_tick = get_system_ms();
    
    // 检查是否过了1秒
    if (current_tick - g_soft_rtc.last_tick >= 1000) {
        g_soft_rtc.last_tick = current_tick;
        
        // 秒数加1
        g_soft_rtc.second++;
        
        // 处理进位
        if (g_soft_rtc.second >= 60) {
            g_soft_rtc.second = 0;
            g_soft_rtc.minute++;
            
            if (g_soft_rtc.minute >= 60) {
                g_soft_rtc.minute = 0;
                g_soft_rtc.hour++;
                
                if (g_soft_rtc.hour >= 24) {
                    g_soft_rtc.hour = 0;
                    g_soft_rtc.day++;
                    
                    int max_days = get_days_in_month(g_soft_rtc.year, g_soft_rtc.month);
                    if (g_soft_rtc.day > max_days) {
                        g_soft_rtc.day = 1;
                        g_soft_rtc.month++;
                        
                        if (g_soft_rtc.month > 12) {
                            g_soft_rtc.month = 1;
                            g_soft_rtc.year++;
                        }
                    }
                }
            }
        }
    }
}

/*!
    \brief      设置软件RTC时间
    \param[in]  year: 年份
    \param[in]  month: 月份
    \param[in]  day: 日期
    \param[in]  hour: 小时
    \param[in]  minute: 分钟
    \param[in]  second: 秒
    \param[out] none
    \retval     int: 0=成功, -1=失败
*/
int software_rtc_set_time(int year, int month, int day, int hour, int minute, int second)
{
    // 验证时间范围
    if (year < 2000 || year > 2099 ||
        month < 1 || month > 12 ||
        day < 1 || day > 31 ||
        hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 ||
        second < 0 || second > 59) {
        return -1;
    }
    
    // 验证日期是否有效
    if (day > get_days_in_month(year, month)) {
        return -1;
    }
    
    // 设置时间
    g_soft_rtc.year = year;
    g_soft_rtc.month = month;
    g_soft_rtc.day = day;
    g_soft_rtc.hour = hour;
    g_soft_rtc.minute = minute;
    g_soft_rtc.second = second;
    g_soft_rtc.last_tick = get_system_ms();
    
    return 0;
}

/*!
    \brief      获取软件RTC时间
    \param[out] year: 年份指针
    \param[out] month: 月份指针
    \param[out] day: 日期指针
    \param[out] hour: 小时指针
    \param[out] minute: 分钟指针
    \param[out] second: 秒指针
    \param[out] none
    \retval     none
*/
void software_rtc_get_time(int *year, int *month, int *day, int *hour, int *minute, int *second)
{
    // 先更新时间
    software_rtc_update();
    
    // 返回当前时间
    *year = g_soft_rtc.year;
    *month = g_soft_rtc.month;
    *day = g_soft_rtc.day;
    *hour = g_soft_rtc.hour;
    *minute = g_soft_rtc.minute;
    *second = g_soft_rtc.second;
}

/*!
    \brief      软件RTC初始化
    \param[in]  none
    \param[out] none
    \retval     none
*/
void software_rtc_init(void)
{
    g_soft_rtc.last_tick = get_system_ms();
}

/*!
    \brief      处理RTC Config命令 - 软件RTC版本
    \param[in]  time_str: 时间字符串
    \param[out] none
    \retval     none
*/
void process_rtc_config_software(const char* time_str)
{
    int year, month, day, hour, minute, second;
    
    // 解析时间字符串
    int parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", 
                       &year, &month, &day, &hour, &minute, &second);
    
    if (parsed == 6) {
        // 设置软件RTC时间
        if (software_rtc_set_time(year, month, day, hour, minute, second) == 0) {
            // 成功
            write_log_entry("rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
                           year, month, day, hour, minute, second);
            my_printf(DEBUG_USART, "RTC Config success         Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                      year, month, day, hour, minute, second);
        } else {
            // 失败
            my_printf(DEBUG_USART, "RTC Config failed\r\n");
        }
    } else {
        // 解析失败
        my_printf(DEBUG_USART, "RTC Config failed\r\n");
    }
}

/*!
    \brief      处理RTC now命令 - 软件RTC版本
    \param[in]  none
    \param[out] none
    \retval     none
*/
void process_rtc_now_software(void)
{
    int year, month, day, hour, minute, second;
    
    // 获取当前软件RTC时间
    software_rtc_get_time(&year, &month, &day, &hour, &minute, &second);
    
    // 按照题目要求的格式输出
    my_printf(DEBUG_USART, "Current Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
              year, month, day, hour, minute, second);
}

/*!
    \brief      软件RTC任务 - 在主循环中调用
    \param[in]  none
    \param[out] none
    \retval     none
*/
void software_rtc_task(void)
{
    software_rtc_update();
}

/*
 * 使用说明：
 * 
 * 1. 在main函数中调用 software_rtc_init()
 * 2. 在主循环中定期调用 software_rtc_task()
 * 3. 在usart_app.c中使用：
 *    - process_rtc_config_software() 替代原来的RTC设置函数
 *    - process_rtc_now_software() 替代原来的RTC读取函数
 * 
 * 这个软件RTC方案的优点：
 * - 不依赖硬件RTC时钟源
 * - 时间会正常运行和更新
 * - 支持闰年计算
 * - 完全符合题目要求的格式
 * - 可以正确处理时间进位（秒->分->时->日->月->年）
 */
