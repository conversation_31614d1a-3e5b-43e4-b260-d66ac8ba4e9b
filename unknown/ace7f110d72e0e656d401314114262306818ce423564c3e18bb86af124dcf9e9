/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/05/15
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"
#include "device_info.h"

int main(void)
{
#ifdef __FIRMWARE_VERSION_DEFINE
    uint32_t fw_ver = 0;
#endif
    systick_config();
    init_cycle_counter(false);
    // 最小延迟，仅确保系统稳定
    delay_ms(50);

#ifdef __FIRMWARE_VERSION_DEFINE
    fw_ver = gd32f4xx_firmware_version_get();
    /* print firmware version */
    //printf("\r\nGD32F4xx series firmware version: V%d.%d.%d", (uint8_t)(fw_ver >> 24), (uint8_t)(fw_ver >> 16), (uint8_t)(fw_ver >> 8));
#endif /* __FIRMWARE_VERSION_DEFINE */

    // Initialize basic peripherals first
    bsp_btn_init();
    bsp_usart_init();

    // 串口初始化完成，无需额外延迟

    // Continue with other peripheral initialization
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_adc_init();
    bsp_dac_init();
    bsp_rtc_init();
    bsp_led_init();
    rtc_basic_init();

    // Initialize device information module
    device_info_init();

    sd_fatfs_init();
    OLED_Init();

    // 初始化配置文件系统
    // config_system_init();  // 取消注释以启用配置文件功能

    // 初始化系统自检模块
    system_selftest_init();

    // 系统启动初始化 - 统一在这里处理所有启动信息打印
    system_startup_init();

    // 初始化软件RTC
    extern void software_rtc_init(void);
    software_rtc_init();

    scheduler_init();
    while(1) {
        scheduler_run();
    }
}

#ifdef GD_ECLIPSE_GCC
/* retarget the C library printf function to the USART, in Eclipse GCC environment */
int __io_putchar(int ch)
{
    usart_data_transmit(EVAL_COM0, (uint8_t)ch);
    while(RESET == usart_flag_get(EVAL_COM0, USART_FLAG_TBE));
    return ch;
}
#else
/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}
#endif /* GD_ECLIPSE_GCC */
