/*
 * 变比设置功能完整测试程序
 * 验证是否完全符合题目要求
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试变比设置的完整流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_ratio_complete_flow(void)
{
    printf("\r\n=== 变比设置完整流程测试 ===\r\n");
    
    printf("题目要求验证：\r\n");
    printf("1. 输入 'ratio' 命令\r\n");
    printf("2. 显示当前变比值和输入提示\r\n");
    printf("3. 输入新的变比值\r\n");
    printf("4. 验证输入有效性（0-100范围）\r\n");
    printf("5. 成功时显示成功消息和新值\r\n");
    printf("6. 失败时显示错误消息和原值\r\n");
    printf("\r\n");
}

/*!
    \brief      测试正常的变比设置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_normal_ratio_setting(void)
{
    printf("\r\n=== 测试：正常变比设置 ===\r\n");
    
    // 测试1：设置为10.5
    printf("测试1：设置变比为10.5\r\n");
    printf("期望输出:\r\n");
    printf("  ratio=1.0\r\n");
    printf("  Input value(0~100):\r\n");
    printf("  ratio modified success ratio=10.5\r\n");
    printf("\r\n实际输出:\r\n");
    
    // 模拟ratio命令
    process_uart_command("ratio");
    
    // 短暂延时
    for(volatile int i = 0; i < 500000; i++);
    
    // 模拟输入10.5
    process_uart_command("10.5");
    
    // 验证当前变比值
    printf("验证：当前变比值 = %.1f (期望: 10.5)\r\n", get_current_ratio());
    
    printf("\r\n");
    
    // 测试2：再次查看变比值
    printf("测试2：再次查看变比值\r\n");
    printf("期望输出:\r\n");
    printf("  ratio=10.5\r\n");
    printf("  Input value(0~100):\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("ratio");
    
    // 模拟输入25.0
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("25.0");
    
    printf("验证：当前变比值 = %.1f (期望: 25.0)\r\n", get_current_ratio());
    
    printf("========================\r\n");
}

/*!
    \brief      测试边界值
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_boundary_values(void)
{
    printf("\r\n=== 测试：边界值 ===\r\n");
    
    // 测试边界值：0.0
    printf("测试1：边界值 0.0\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("0.0");
    printf("验证：当前变比值 = %.1f (期望: 0.0)\r\n", get_current_ratio());
    printf("\r\n");
    
    // 测试边界值：100.0
    printf("测试2：边界值 100.0\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("100.0");
    printf("验证：当前变比值 = %.1f (期望: 100.0)\r\n", get_current_ratio());
    printf("\r\n");
    
    // 测试中间值：50.5
    printf("测试3：中间值 50.5\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("50.5");
    printf("验证：当前变比值 = %.1f (期望: 50.5)\r\n", get_current_ratio());
    
    printf("========================\r\n");
}

/*!
    \brief      测试错误输入
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_invalid_inputs(void)
{
    printf("\r\n=== 测试：错误输入 ===\r\n");
    
    // 先设置一个已知值
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("30.0");
    float original_ratio = get_current_ratio();
    printf("设置初始值为: %.1f\r\n\r\n", original_ratio);
    
    // 测试1：超出上限
    printf("测试1：超出上限 (100.5)\r\n");
    printf("期望输出:\r\n");
    printf("  ratio=30.0\r\n");
    printf("  Input value(0~100):\r\n");
    printf("  ratio invalid\r\n");
    printf("  ratio=30.0\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("100.5");
    printf("验证：变比值未改变 = %.1f (期望: 30.0)\r\n", get_current_ratio());
    printf("\r\n");
    
    // 测试2：负值
    printf("测试2：负值 (-5.0)\r\n");
    printf("期望输出:\r\n");
    printf("  ratio invalid\r\n");
    printf("  ratio=30.0\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("-5.0");
    printf("验证：变比值未改变 = %.1f (期望: 30.0)\r\n", get_current_ratio());
    printf("\r\n");
    
    // 测试3：非数字
    printf("测试3：非数字 (abc)\r\n");
    printf("期望输出:\r\n");
    printf("  ratio invalid\r\n");
    printf("  ratio=30.0\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("abc");
    printf("验证：变比值未改变 = %.1f (期望: 30.0)\r\n", get_current_ratio());
    printf("\r\n");
    
    // 测试4：空输入
    printf("测试4：空输入\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("");
    printf("验证：变比值未改变 = %.1f (期望: 30.0)\r\n", get_current_ratio());
    
    printf("========================\r\n");
}

/*!
    \brief      测试浮点数精度
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_float_precision(void)
{
    printf("\r\n=== 测试：浮点数精度 ===\r\n");
    
    const char* test_values[] = {
        "1.0",      // 简单小数
        "10.5",     // 一位小数
        "99.99",    // 两位小数
        "0.1",      // 小于1的数
        "50",       // 整数
        "12.345",   // 多位小数
        NULL
    };
    
    for (int i = 0; test_values[i] != NULL; i++) {
        printf("测试值: %s\r\n", test_values[i]);
        process_uart_command("ratio");
        for(volatile int j = 0; j < 300000; j++);
        process_uart_command((char*)test_values[i]);
        printf("结果: %.3f\r\n\r\n", get_current_ratio());
    }
    
    printf("========================\r\n");
}

/*!
    \brief      验证输出格式是否完全符合题目要求
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_output_format(void)
{
    printf("\r\n=== 验证输出格式 ===\r\n");
    
    printf("题目要求的输出格式：\r\n");
    printf("正常情况:\r\n");
    printf("  输入: ratio\r\n");
    printf("  输出: ratio=x.x\r\n");
    printf("        Input value(0~100):\r\n");
    printf("  输入: 10.5\r\n");
    printf("  输出: ratio modified success ratio=10.5\r\n");
    printf("\r\n");
    printf("错误情况:\r\n");
    printf("  输入: ratio\r\n");
    printf("  输出: ratio=x.x\r\n");
    printf("        Input value(0~100):\r\n");
    printf("  输入: 100.5\r\n");
    printf("  输出: ratio invalid\r\n");
    printf("        ratio=x.x\r\n");
    printf("\r\n");
    
    printf("实际测试输出：\r\n");
    
    // 设置一个已知值
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("15.5");
    
    printf("\r\n正常情况测试：\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("20.5");
    
    printf("\r\n错误情况测试：\r\n");
    process_uart_command("ratio");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("150.0");
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*!
    \brief      完整的变比功能测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ratio_complete_function_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        变比设置功能完整测试          #\r\n");
    printf("##########################################\r\n");
    
    // 1. 显示测试说明
    test_ratio_complete_flow();
    
    // 2. 正常设置测试
    test_normal_ratio_setting();
    
    // 3. 边界值测试
    test_boundary_values();
    
    // 4. 错误输入测试
    test_invalid_inputs();
    
    // 5. 浮点数精度测试
    test_float_precision();
    
    // 6. 输出格式验证
    verify_output_format();
    
    printf("\r\n##########################################\r\n");
    printf("#        测试完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n现在可以通过串口手动测试：\r\n");
    printf("1. 发送 'ratio' 命令\r\n");
    printf("2. 输入变比值 (0-100)\r\n");
    printf("3. 观察输出格式是否正确\r\n");
    printf("4. 测试各种边界值和错误输入\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行测试：
 * 
 * 1. ratio_complete_function_test();  // 完整功能测试
 * 2. test_normal_ratio_setting();     // 正常设置测试
 * 3. test_invalid_inputs();           // 错误输入测试
 * 4. verify_output_format();          // 格式验证
 * 
 * 然后通过串口发送以下命令进行实际测试：
 * - "ratio" -> 应该显示当前变比值和输入提示
 * - "10.5" -> 应该返回 "ratio modified success ratio=10.5"
 * - "100.5" -> 应该返回 "ratio invalid" 和当前变比值
 * 
 * 注意：题目示例中的输出可能有错误，正确的应该是：
 * 输入: 10.5
 * 输出: ratio modified success ratio=10.5  (而不是ratio=1.0)
 */
