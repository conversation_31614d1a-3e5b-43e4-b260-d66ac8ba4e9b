# 系统自检功能使用说明

## 功能概述

系统自检功能通过串口命令触发，对系统的关键硬件模块进行检测，并输出标准化的测试结果。

## ⚠️ 重要说明

**当前实现方式**: 由于 `system_selftest.c` 文件尚未添加到Keil项目中，系统自检功能已临时集成到 `usart_app.c` 文件中，确保编译通过并正常工作。

**建议**: 在Keil项目中添加 `sysFunction/system_selftest.c` 文件到编译列表，然后可以将自检功能从 `usart_app.c` 中移除，恢复到独立模块。

## 使用方法

### 1. 触发自检
通过串口发送命令：
```
test
```

### 2. 输出格式

#### 成功案例（所有模块正常）
```
====== system selftest======
flash………………ok
TF card………………ok
flash ID:0xC84015
TF card memory: 1024 KB
RTC:2025-01-01 01:00:50
====== system selftest======
```

#### 失败案例（TF卡异常）
```
====== system selftest======
flash………………ok
TF card………………error
flash ID:0xC84015
can not find TF card
RTC:2025-01-01 01:00:50
====== system selftest======
```

## 检测项目

### 1. Flash 检测
- **检测方法**: 读取SPI Flash ID
- **判断标准**: ID不为0x000000或0xFFFFFF
- **成功输出**: `flash………………ok` + `flash ID:0xCxxxxx`
- **失败输出**: `flash………………error` + `can not read flash ID`

### 2. TF卡检测
- **检测方法**: 尝试初始化SD卡并读取容量
- **判断标准**: SD卡初始化成功
- **成功输出**: `TF card………………ok` + `TF card memory: xxxx KB`
- **失败输出**: `TF card………………error` + `can not find TF card`

### 3. RTC检测
- **检测方法**: 读取RTC时间并验证合理性
- **判断标准**: 时、分、秒在有效范围内
- **输出格式**: `RTC:20xx-xx-xx xx:xx:xx`

## 代码结构

### 主要文件
- `sysFunction/system_selftest.c` - 自检功能实现
- `sysFunction/system_selftest.h` - 自检功能头文件
- `sysFunction/usart_app.c` - 串口命令处理
- `sysFunction/selftest_demo.c` - 演示和测试代码

### 关键函数
- `system_selftest_run()` - 执行完整自检流程
- `selftest_flash()` - Flash模块检测
- `selftest_tfcard()` - TF卡模块检测
- `selftest_rtc()` - RTC模块检测
- `process_uart_command()` - 串口命令解析

## 集成说明

1. 系统自检已集成到主调度器中
2. 串口命令处理在 `uart_task()` 中周期性执行
3. 发送"test"命令即可触发自检
4. 所有输出通过DEBUG_USART串口发送

## 扩展建议

可以根据需要添加更多检测项目：
- ADC模块检测
- DAC模块检测
- 按键功能检测
- LED功能检测
- OLED显示检测
