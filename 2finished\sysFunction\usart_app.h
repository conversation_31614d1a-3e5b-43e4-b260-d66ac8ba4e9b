#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

int my_printf(uint32_t usart_periph, const char *format, ...);
void uart_task(void);
void process_uart_command(char* command);
void show_rtc_time(void);
void show_current_time(void);
void process_rtc_time_input(char* time_str);
uint8_t decimal_to_bcd(uint8_t decimal);
uint8_t bcd_to_decimal(uint8_t bcd);

// 变比设置相关函数
void process_ratio_input(char* ratio_str);
void process_ratio_command(void);
float get_current_ratio(void);

// 配置文件读取相关函数
void process_conf_command(void);
float get_config_ratio(void);
float get_config_limit(void);

// 阈值设置相关函数
void process_limit_input(char* limit_str);
void process_limit_command(void);
float get_current_limit(void);

// 采样控制相关函数
float get_channel_voltage(uint8_t channel);
void start_periodic_sampling(void);
void stop_periodic_sampling(void);
void toggle_sampling_state(void);
void set_sample_cycle(uint32_t cycle);
void update_oled_display(void);
uint32_t get_rtc_timestamp(void);
void sampling_task(void);
void led_blink_task(uint32_t current_time_ms);

// 按键处理和配置持久化
void btn_task(void);
void save_sample_cycle_to_flash(uint32_t cycle);
uint32_t load_sample_cycle_from_flash(void);

// 辅助函数
uint32_t get_sample_cycle(void);
void set_current_ratio(float ratio);

// 系统功能相关函数
void system_startup_init(void);
void system_selftest(void);
void config_save_to_flash(void);
void config_read_from_flash(void);
void load_config_from_flash_silent(void);

// 数据处理相关函数
uint32_t time_to_unix_timestamp(uint16_t year, uint8_t month, uint8_t day,
                                uint8_t hour, uint8_t minute, uint8_t second);
void start_hide_mode(void);
void stop_hide_mode(void);

// RTC时间管理相关函数
void process_rtc_config_command(void);
void process_rtc_datetime_input(char* datetime_str);
void process_rtc_now_command(void);

// 采集数据存储功能相关函数
int init_sample_folder(void);
void generate_sample_filename(uint8_t year, uint8_t month, uint8_t day,
                             uint8_t hour, uint8_t minute, uint8_t second,
                             char* filename);
int create_new_sample_file(uint8_t year, uint8_t month, uint8_t day,
                          uint8_t hour, uint8_t minute, uint8_t second);
void close_current_sample_file(void);
int write_sample_data(uint8_t year, uint8_t month, uint8_t day,
                     uint8_t hour, uint8_t minute, uint8_t second,
                     float voltage, uint8_t is_overlimit);
int check_tf_card_status(void);
void get_file_system_status(void);
void reset_file_manager(void);
void cleanup_on_sampling_stop(void);

// 超限数据存储功能相关函数
int init_overlimit_folder(void);
void generate_overlimit_filename(uint8_t year, uint8_t month, uint8_t day,
                                uint8_t hour, uint8_t minute, uint8_t second,
                                char* filename);
int create_new_overlimit_file(uint8_t year, uint8_t month, uint8_t day,
                             uint8_t hour, uint8_t minute, uint8_t second);
void close_current_overlimit_file(void);
int write_overlimit_data(uint8_t year, uint8_t month, uint8_t day,
                        uint8_t hour, uint8_t minute, uint8_t second,
                        float voltage, float limit);

// 加密数据存储功能相关函数
int init_hidedata_folder(void);
void generate_hidedata_filename(uint8_t year, uint8_t month, uint8_t day,
                               uint8_t hour, uint8_t minute, uint8_t second,
                               char* filename);
int create_new_hidedata_file(uint8_t year, uint8_t month, uint8_t day,
                            uint8_t hour, uint8_t minute, uint8_t second);
void close_current_hidedata_file(void);
int write_hidedata(uint8_t year, uint8_t month, uint8_t day,
                  uint8_t hour, uint8_t minute, uint8_t second,
                  float voltage);

// 操作审计（日志记录）功能相关函数
uint32_t load_boot_count_from_flash(void);
void save_boot_count_to_flash(uint32_t count);
uint32_t increment_boot_count(void);
int init_log_folder(void);
int create_log_file(uint32_t boot_id);
void close_current_log_file(void);
int write_log_entry(const char* format, ...);

// TF卡测试功能
void test_tf_card_functionality(void);

#ifdef __cplusplus
}
#endif

#endif
