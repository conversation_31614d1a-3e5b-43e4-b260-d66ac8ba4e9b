#ifndef INI_PARSER_H
#define INI_PARSER_H

#include "ff.h"     // FatFS文件系统

// INI解析状态枚举
typedef enum
{
    INI_OK = 0,              // 解析成功
    INI_ERROR = -1,          // 一般错误
    INI_FILE_NOT_FOUND = -2, // 文件未找到
    INI_FORMAT_ERROR = -3,   // 格式错误
    INI_VALUE_ERROR = -4     // 数值错误
} ini_status_t;

// INI配置结构体
typedef struct
{
    float ratio;        // 变比值
    float limit;        // 阈值
    int ratio_found;    // 是否找到ratio配置
    int limit_found;    // 是否找到limit配置
} ini_config_t;

// 函数声明

/*!
    \brief      去除字符串前后空格
    \param[in]  str: 字符串指针
    \param[out] none
    \retval     ini_status_t: 解析状态
*/
ini_status_t ini_trim_string(char *str);

/*!
    \brief      解析浮点数值
    \param[in]  str: 字符串, value: 浮点数指针
    \param[out] none
    \retval     ini_status_t: 解析状态
*/
ini_status_t ini_parse_float(const char *str, float *value);

/*!
    \brief      解析单行数据
    \param[in]  line: 行字符串, config: 配置结构体指针
    \param[out] none
    \retval     ini_status_t: 解析状态
*/
ini_status_t ini_parse_line(const char *line, ini_config_t *config);

/*!
    \brief      解析INI配置文件
    \param[in]  filename: 文件名, config: 配置结构体指针
    \param[out] none
    \retval     ini_status_t: 解析状态
*/
ini_status_t ini_parse_file(const char *filename, ini_config_t *config);

#endif // INI_PARSER_H
