/*
 * RTC集成测试头文件
 * 提供简单的测试接口
 */

#ifndef __RTC_INTEGRATION_TEST_H__
#define __RTC_INTEGRATION_TEST_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 测试函数声明
void test_rtc_complete_flow(void);
void check_rtc_registers(void);
void check_rtc_clock_source(void);
void test_uart_receive(void);
void manual_test_rtc_setting(void);
void check_scheduler_tasks(void);
void rtc_system_diagnosis(void);
void simple_rtc_verification(void);

// 快速测试宏
#define RTC_QUICK_TEST() do { \
    printf("\r\n=== RTC快速测试 ===\r\n"); \
    simple_rtc_verification(); \
    printf("请通过串口发送以下命令测试：\r\n"); \
    printf("1. RTC Config\r\n"); \
    printf("2. 2025-01-01 15:00:10\r\n"); \
    printf("3. RTC now\r\n"); \
    printf("==================\r\n"); \
} while(0)

#ifdef __cplusplus
}
#endif

#endif /* __RTC_INTEGRATION_TEST_H__ */
