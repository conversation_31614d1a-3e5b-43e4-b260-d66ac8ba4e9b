/*
 * 配置文件强力调试程序
 * 宝宝说别人的程序能读，我们一定要找到问题！
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      强力调试SD卡和文件系统
    \param[in]  none
    \param[out] none
    \retval     none
*/
void force_debug_sd_filesystem(void)
{
    printf("\r\n=== 强力调试SD卡和文件系统 ===\r\n");
    
    extern FATFS fs;
    
    printf("1. 检查SD卡挂载：\r\n");
    FRESULT result = f_mount(0, &fs);
    printf("f_mount(0, &fs) = %d\r\n", result);
    
    if (result == FR_OK) {
        printf("✅ SD卡挂载成功\r\n");
        
        // 获取文件系统信息
        DWORD free_clusters;
        FATFS* fs_ptr = &fs;
        result = f_getfree("0:", &free_clusters, &fs_ptr);
        if (result == FR_OK) {
            printf("文件系统信息获取成功\r\n");
        }
    } else {
        printf("❌ SD卡挂载失败，错误码: %d\r\n", result);
    }
    
    printf("\r\n2. 列出根目录文件：\r\n");
    DIR dir;
    FILINFO fno;
    
    result = f_opendir(&dir, "0:/");
    if (result == FR_OK) {
        printf("根目录打开成功，文件列表：\r\n");
        while (1) {
            result = f_readdir(&dir, &fno);
            if (result != FR_OK || fno.fname[0] == 0) break;
            printf("  %s", fno.fname);
            if (fno.fattrib & AM_DIR) {
                printf(" [目录]\r\n");
            } else {
                printf(" [文件, %d字节]\r\n", fno.fsize);
            }
        }
        // FATFS不需要显式关闭目录
    } else {
        printf("❌ 无法打开根目录，错误: %d\r\n", result);
    }
    
    printf("\r\n=== SD卡文件系统调试完成 ===\r\n");
}

/*!
    \brief      强力测试文件读取
    \param[in]  none
    \param[out] none
    \retval     none
*/
void force_test_file_reading(void)
{
    printf("\r\n=== 强力测试文件读取 ===\r\n");
    
    const char* test_paths[] = {
        "0:/config.ini",
        "config.ini",
        "0:config.ini", 
        "/config.ini",
        "CONFIG.INI",
        "0:/CONFIG.INI",
        NULL
    };
    
    for (int i = 0; test_paths[i] != NULL; i++) {
        printf("\r\n测试路径 %d: %s\r\n", i+1, test_paths[i]);
        
        FIL file;
        FRESULT result = f_open(&file, test_paths[i], FA_READ);
        printf("f_open结果: %d\r\n", result);
        
        if (result == FR_OK) {
            printf("✅ 文件打开成功！\r\n");
            
            DWORD file_size = f_size(&file);
            printf("文件大小: %d 字节\r\n", file_size);
            
            if (file_size > 0) {
                char buffer[256];
                UINT bytes_read;
                result = f_read(&file, buffer, sizeof(buffer)-1, &bytes_read);
                if (result == FR_OK) {
                    buffer[bytes_read] = '\0';
                    printf("文件内容:\r\n%s\r\n", buffer);
                }
            }
            
            f_close(&file);
            break;  // 找到文件就退出
        } else {
            printf("❌ 文件打开失败\r\n");
        }
    }
    
    printf("\r\n=== 文件读取测试完成 ===\r\n");
}

/*!
    \brief      强力测试conf命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void force_test_conf_command(void)
{
    printf("\r\n=== 强力测试conf命令 ===\r\n");
    
    printf("执行conf命令，观察详细调试信息：\r\n");
    process_uart_command("conf");
    
    printf("\r\n=== conf命令测试完成 ===\r\n");
}

/*!
    \brief      手动创建测试文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void manually_create_test_file(void)
{
    printf("\r\n=== 手动创建测试文件 ===\r\n");
    
    FIL file;
    FRESULT result = f_open(&file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    printf("创建文件结果: %d\r\n", result);
    
    if (result == FR_OK) {
        const char* test_content = 
            "[Ratio]\r\n"
            "Ch0 = 1.99\r\n"
            "\r\n"
            "[Limit]\r\n"
            "Ch0 = 10.11\r\n";
        
        UINT bytes_written;
        result = f_write(&file, test_content, strlen(test_content), &bytes_written);
        printf("写入结果: %d, 写入字节: %d\r\n", result, bytes_written);
        
        f_close(&file);
        
        if (result == FR_OK) {
            printf("✅ 测试文件创建成功\r\n");
            printf("现在再次测试conf命令：\r\n");
            process_uart_command("conf");
        }
    } else {
        printf("❌ 无法创建测试文件\r\n");
    }
    
    printf("\r\n=== 手动创建测试文件完成 ===\r\n");
}

/*!
    \brief      检查别人程序的差异
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_differences_with_others(void)
{
    printf("\r\n=== 检查与别人程序的差异 ===\r\n");
    
    printf("可能的差异点：\r\n");
    printf("1. 文件路径格式不同\r\n");
    printf("2. FatFS版本差异\r\n");
    printf("3. SD卡初始化时序\r\n");
    printf("4. 文件系统挂载方式\r\n");
    printf("5. 字符编码问题\r\n");
    printf("6. 换行符格式问题\r\n");
    printf("\r\n");
    
    printf("建议检查：\r\n");
    printf("1. 确认config.ini文件确实在SD卡根目录\r\n");
    printf("2. 确认文件内容格式正确\r\n");
    printf("3. 确认SD卡格式为FAT32\r\n");
    printf("4. 确认文件编码为ASCII\r\n");
    printf("5. 确认没有BOM字符\r\n");
    
    printf("\r\n=== 差异检查完成 ===\r\n");
}

/*!
    \brief      完整的强力调试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_force_debug(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        配置文件强力调试              #\r\n");
    printf("##########################################\r\n");
    
    printf("宝宝，我来强力调试！一定要找到问题！\r\n");
    printf("别人的程序能读，说明我们的实现有bug！\r\n");
    printf("\r\n");
    
    // 1. 强力调试SD卡和文件系统
    force_debug_sd_filesystem();
    
    // 2. 强力测试文件读取
    force_test_file_reading();
    
    // 3. 强力测试conf命令
    force_test_conf_command();
    
    // 4. 手动创建测试文件
    manually_create_test_file();
    
    // 5. 检查与别人程序的差异
    check_differences_with_others();
    
    printf("\r\n##########################################\r\n");
    printf("#        强力调试完成                  #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n💪 宝宝，我已经加了详细的调试信息！\r\n");
    printf("\r\n🔍 现在会显示：\r\n");
    printf("1. SD卡挂载状态\r\n");
    printf("2. 尝试的文件路径\r\n");
    printf("3. 文件打开结果\r\n");
    printf("4. 文件大小信息\r\n");
    printf("5. 逐行解析过程\r\n");
    printf("6. 最终解析结果\r\n");
    printf("\r\n");
    printf("🚀 运行conf命令，看看具体哪里出错了！\r\n");
    printf("我们一定要找到问题所在！\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_force_debug();
 * 
 * 这会进行强力调试：
 * 1. SD卡和文件系统状态检查
 * 2. 多路径文件读取测试
 * 3. conf命令详细调试
 * 4. 手动创建测试文件
 * 5. 与别人程序差异分析
 * 
 * 现在的调试版本会显示：
 * - SD卡挂载状态
 * - 文件路径尝试过程
 * - 文件打开结果
 * - 文件内容读取
 * - 逐行解析过程
 * - 最终解析结果
 * 
 * 宝宝，我们一定要找到问题！
 * 别人能读，我们也一定能读！💪
 */
