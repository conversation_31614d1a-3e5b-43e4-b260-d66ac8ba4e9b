File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.398987%,13095,96,12544,551,0,96
usart_app.o,14.790800%,11132,3061,10866,193,73,2988
ff.o,10.479253%,7887,518,7740,141,6,512
sdio_sdcard.o,9.526593%,7170,68,7134,0,36,32
oled.o,5.282808%,3976,22,1242,2712,22,0
btod.o,2.859307%,2152,0,2152,0,0,0
mcu_cmic_gd32f470vet6.o,2.322522%,1748,592,1728,0,20,572
fz_wm.l,2.022242%,1522,0,1506,16,0,0
gd32f4xx_dma.o,1.748535%,1316,0,1316,0,0,0
scanf_fp.o,1.690073%,1272,0,1272,0,0,0
system_selftest.o,1.429653%,1076,492,1076,0,0,492
_printf_fp_dec.o,1.400423%,1054,0,1054,0,0,0
gd25qxx.o,1.315387%,990,0,990,0,0,0
_scanf.o,1.174548%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.147974%,864,0,864,0,0,0
perf_counter.o,1.126716%,848,64,780,4,64,0
_printf_fp_hex.o,1.065597%,802,0,764,38,0,0
scanf_hexfp.o,1.062939%,800,0,800,0,0,0
m_wm.l,1.033708%,778,0,778,0,0,0
gd32f4xx_usart.o,0.972589%,732,0,732,0,0,0
system_gd32f4xx.o,0.927415%,698,4,694,0,4,0
gd32f4xx_adc.o,0.868953%,654,0,654,0,0,0
gd32f4xx_sdio.o,0.834407%,628,0,628,0,0,0
gd32f4xx_timer.o,0.781260%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.659022%,496,0,496,0,0,0
startup_gd32f450_470.o,0.653708%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.643078%,484,0,484,0,0,0
task_key.o,0.555386%,418,6,412,0,6,0
__printf_flags_ss_wp.o,0.543428%,409,0,392,17,0,0
bigflt0.o,0.499581%,376,0,228,148,0,0
dmul.o,0.451749%,340,0,340,0,0,0
_scanf_int.o,0.441120%,332,0,332,0,0,0
lc_ctype_c.o,0.419861%,316,0,44,272,0,0
diskio.o,0.419861%,316,0,316,0,0,0
scanf_infnan.o,0.409232%,308,0,308,0,0,0
narrow.o,0.353427%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.348113%,262,0,262,0,0,0
lludivv7m.o,0.316224%,238,0,238,0,0,0
ldexp.o,0.302938%,228,0,228,0,0,0
gd32f4xx_misc.o,0.286994%,216,0,216,0,0,0
gd32f4xx_dac.o,0.263077%,198,0,198,0,0,0
_printf_wctomb.o,0.260420%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.249791%,188,0,148,40,0,0
scheduler.o,0.249791%,188,88,100,0,88,0
_printf_intcommon.o,0.236504%,178,0,178,0,0,0
systick.o,0.223217%,168,4,164,0,4,0
strtod.o,0.217903%,164,0,164,0,0,0
dnaninf.o,0.207273%,156,0,156,0,0,0
perfc_port_default.o,0.204616%,154,0,154,0,0,0
strncmp.o,0.199301%,150,0,150,0,0,0
frexp.o,0.186014%,140,0,140,0,0,0
fnaninf.o,0.186014%,140,0,140,0,0,0
rt_memcpy_v6.o,0.183357%,138,0,138,0,0,0
lludiv10.o,0.183357%,138,0,138,0,0,0
rt_memmove_v6.o,0.175385%,132,0,132,0,0,0
strcmpv7m.o,0.170070%,128,0,128,0,0,0
_printf_fp_infnan.o,0.170070%,128,0,128,0,0,0
gd32f4xx_it.o,0.170070%,128,0,128,0,0,0
_printf_longlong_dec.o,0.164756%,124,0,124,0,0,0
rt_memmove_w.o,0.162098%,122,0,122,0,0,0
dleqf.o,0.159441%,120,0,120,0,0,0
deqf.o,0.159441%,120,0,120,0,0,0
_printf_dec.o,0.159441%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.148812%,112,0,112,0,0,0
drleqf.o,0.143497%,108,0,108,0,0,0
device_info.o,0.143497%,108,52,108,0,0,52
gd32f4xx_spi.o,0.138182%,104,0,104,0,0,0
retnan.o,0.132867%,100,0,100,0,0,0
rt_memcpy_w.o,0.132867%,100,0,100,0,0,0
d2f.o,0.130210%,98,0,98,0,0,0
rtc_app.o,0.127553%,96,0,96,0,0,0
main.o,0.124895%,94,0,94,0,0,0
scalbn.o,0.122238%,92,0,92,0,0,0
__dczerorl2.o,0.119581%,90,0,90,0,0,0
f2d.o,0.114266%,86,0,86,0,0,0
strncpy.o,0.114266%,86,0,86,0,0,0
_printf_str.o,0.108951%,82,0,82,0,0,0
rt_memclr_w.o,0.103637%,78,0,78,0,0,0
_printf_pad.o,0.103637%,78,0,78,0,0,0
led_app.o,0.103637%,78,2,76,0,2,0
sys_stackheap_outer.o,0.098322%,74,0,74,0,0,0
strcpy.o,0.095665%,72,0,72,0,0,0
llsdiv.o,0.095665%,72,0,72,0,0,0
lc_numeric_c.o,0.095665%,72,0,44,28,0,0
unicode.o,0.095665%,72,0,72,0,0,0
rt_memclr.o,0.090350%,68,0,68,0,0,0
dunder.o,0.085035%,64,0,64,0,0,0
_wcrtomb.o,0.085035%,64,0,64,0,0,0
_sgetc.o,0.085035%,64,0,64,0,0,0
strlen.o,0.082378%,62,0,62,0,0,0
__0sscanf.o,0.079720%,60,0,60,0,0,0
__2snprintf.o,0.074406%,56,0,56,0,0,0
vsnprintf.o,0.069091%,52,0,52,0,0,0
__scatter.o,0.069091%,52,0,52,0,0,0
fpclassify.o,0.063776%,48,0,48,0,0,0
trapv.o,0.063776%,48,0,48,0,0,0
_printf_char_common.o,0.063776%,48,0,48,0,0,0
scanf_char.o,0.058462%,44,0,44,0,0,0
_printf_wchar.o,0.058462%,44,0,44,0,0,0
_printf_char.o,0.058462%,44,0,44,0,0,0
_printf_charcount.o,0.053147%,40,0,40,0,0,0
llshl.o,0.050490%,38,0,38,0,0,0
libinit2.o,0.050490%,38,0,38,0,0,0
strstr.o,0.047832%,36,0,36,0,0,0
init_aeabi.o,0.047832%,36,0,36,0,0,0
_printf_truncate.o,0.047832%,36,0,36,0,0,0
strtof.o,0.042518%,32,0,32,0,0,0
systick_wrapper_ual.o,0.042518%,32,0,32,0,0,0
_chval.o,0.037203%,28,0,28,0,0,0
__scatter_zi.o,0.037203%,28,0,28,0,0,0
strtof.o,0.034546%,26,0,26,0,0,0
dcmpi.o,0.031888%,24,0,24,0,0,0
_rserrno.o,0.029231%,22,0,22,0,0,0
strchr.o,0.026573%,20,0,20,0,0,0
gd32f4xx_pmu.o,0.026573%,20,0,20,0,0,0
adc_app.o,0.026573%,20,0,20,0,0,0
isspace.o,0.023916%,18,0,18,0,0,0
exit.o,0.023916%,18,0,18,0,0,0
fpconst.o,0.021259%,16,0,0,16,0,0
dcheck1.o,0.021259%,16,0,16,0,0,0
rt_ctype_table.o,0.021259%,16,0,16,0,0,0
_snputc.o,0.021259%,16,0,16,0,0,0
__printf_wp.o,0.018601%,14,0,14,0,0,0
sd_app.o,0.018601%,14,1436,14,0,0,1436
dretinf.o,0.015944%,12,0,12,0,0,0
sys_exit.o,0.015944%,12,0,12,0,0,0
__rtentry2.o,0.015944%,12,0,12,0,0,0
fretinf.o,0.013287%,10,0,10,0,0,0
fpinit.o,0.013287%,10,0,10,0,0,0
rtexit2.o,0.013287%,10,0,10,0,0,0
_sputc.o,0.013287%,10,0,10,0,0,0
_printf_ll.o,0.013287%,10,0,10,0,0,0
_printf_l.o,0.013287%,10,0,10,0,0,0
scanf2.o,0.010629%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010629%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010629%,8,0,8,0,0,0
libspace.o,0.010629%,8,96,8,0,0,96
__main.o,0.010629%,8,0,8,0,0,0
oled_app.o,0.010629%,8,0,8,0,0,0
istatus.o,0.007972%,6,0,6,0,0,0
heapauxi.o,0.007972%,6,0,6,0,0,0
_printf_x.o,0.007972%,6,0,6,0,0,0
_printf_u.o,0.007972%,6,0,6,0,0,0
_printf_s.o,0.007972%,6,0,6,0,0,0
_printf_p.o,0.007972%,6,0,6,0,0,0
_printf_o.o,0.007972%,6,0,6,0,0,0
_printf_n.o,0.007972%,6,0,6,0,0,0
_printf_ls.o,0.007972%,6,0,6,0,0,0
_printf_llx.o,0.007972%,6,0,6,0,0,0
_printf_llu.o,0.007972%,6,0,6,0,0,0
_printf_llo.o,0.007972%,6,0,6,0,0,0
_printf_lli.o,0.007972%,6,0,6,0,0,0
_printf_lld.o,0.007972%,6,0,6,0,0,0
_printf_lc.o,0.007972%,6,0,6,0,0,0
_printf_i.o,0.007972%,6,0,6,0,0,0
_printf_g.o,0.007972%,6,0,6,0,0,0
_printf_f.o,0.007972%,6,0,6,0,0,0
_printf_e.o,0.007972%,6,0,6,0,0,0
_printf_d.o,0.007972%,6,0,6,0,0,0
_printf_c.o,0.007972%,6,0,6,0,0,0
_printf_a.o,0.007972%,6,0,6,0,0,0
__rtentry4.o,0.007972%,6,0,6,0,0,0
scanf1.o,0.005315%,4,0,4,0,0,0
printf2.o,0.005315%,4,0,4,0,0,0
printf1.o,0.005315%,4,0,4,0,0,0
_printf_percent_end.o,0.005315%,4,0,4,0,0,0
use_no_semi.o,0.002657%,2,0,2,0,0,0
rtexit.o,0.002657%,2,0,2,0,0,0
libshutdown2.o,0.002657%,2,0,2,0,0,0
libshutdown.o,0.002657%,2,0,2,0,0,0
libinit.o,0.002657%,2,0,2,0,0,0
