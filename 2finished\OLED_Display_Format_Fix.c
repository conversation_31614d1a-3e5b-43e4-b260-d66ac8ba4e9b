/*
 * 🔧 OLED显示格式修复验证
 * 确保OLED正确显示时间和电压格式
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      OLED显示格式问题分析
    \param[in]  none
    \param[out] none
    \retval     none
*/
void oled_display_format_problem_analysis(void)
{
    printf("\r\n=== OLED显示格式问题分析 ===\r\n");
    
    printf("📋 宝宝的要求:\r\n");
    printf("串口输出: 2025-01-01 00:30:05 ch0=10.5V\r\n");
    printf("OLED显示:\r\n");
    printf("  第一行: 00:30:05 (只显示时分秒，格式hh:mm:ss)\r\n");
    printf("  第二行: 10.50 V (保留两位小数，格式xx.xx V)\r\n");
    
    printf("\r\n🔍 当前代码分析:\r\n");
    printf("update_oled_display()函数中:\r\n");
    printf("1. 时间格式: snprintf(time_str, \"%%02d:%%02d:%%02d\", hour, minute, second)\r\n");
    printf("   → 应该输出: 00:30:05 ✓\r\n");
    printf("2. 电压格式: snprintf(voltage_str, \"%%.2f V\", voltage)\r\n");
    printf("   → 应该输出: 10.50 V ✓\r\n");
    
    printf("\r\n🎯 可能的问题:\r\n");
    printf("1. OLED硬件连接问题\r\n");
    printf("2. OLED初始化问题\r\n");
    printf("3. update_oled_display()没有被调用\r\n");
    printf("4. 条件判断问题 (uart_flag && sampling_active)\r\n");
    printf("5. 时间转换问题 (BCD转换)\r\n");
    printf("6. 电压计算问题\r\n");
    
    printf("\r\n=== 问题分析完成 ===\r\n");
}

/*!
    \brief      OLED显示测试函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_oled_display_step_by_step(void)
{
    printf("\r\n=== OLED显示逐步测试 ===\r\n");
    
    printf("🧪 测试步骤:\r\n");
    
    printf("\r\n步骤1: 测试OLED基础显示\r\n");
    printf("显示固定文本测试OLED是否工作:\r\n");
    OLED_ShowStr(0, 0, "TEST 123", 8);
    OLED_ShowStr(0, 2, "HELLO", 8);
    printf("如果OLED显示'TEST 123'和'HELLO'，说明OLED硬件正常\r\n");
    
    printf("\r\n步骤2: 测试时间格式\r\n");
    char time_test[16];
    snprintf(time_test, sizeof(time_test), "%02d:%02d:%02d", 12, 34, 56);
    OLED_ShowStr(0, 0, time_test, 8);
    printf("OLED第一行应该显示: 12:34:56\r\n");
    
    printf("\r\n步骤3: 测试电压格式\r\n");
    char voltage_test[16];
    float test_voltage = 10.5f;
    snprintf(voltage_test, sizeof(voltage_test), "%.2f V", test_voltage);
    OLED_ShowStr(0, 2, voltage_test, 8);
    printf("OLED第二行应该显示: 10.50 V\r\n");
    
    printf("\r\n步骤4: 测试system idle显示\r\n");
    OLED_ShowStr(0, 0, "system idle", 8);
    OLED_ShowStr(0, 2, "           ", 8);  // 清空第二行
    printf("OLED应该显示: system idle (第一行), 空 (第二行)\r\n");
    
    printf("\r\n=== 逐步测试完成 ===\r\n");
}

/*!
    \brief      调试版本的OLED更新函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_update_oled_display(void)
{
    printf("\r\n=== 调试版本OLED更新 ===\r\n");
    
    // 打印状态信息
    printf("uart_flag = %d\r\n", uart_flag);
    printf("sampling_active = %d\r\n", sampling_active);
    
    if (uart_flag && sampling_active) {
        printf("进入采样状态显示分支\r\n");
        
        // 获取RTC时间
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        
        // 转换BCD到十进制
        uint8_t hour = bcd_to_decimal(rtc_time.hour);
        uint8_t minute = bcd_to_decimal(rtc_time.minute);
        uint8_t second = bcd_to_decimal(rtc_time.second);
        
        printf("RTC时间: %02d:%02d:%02d\r\n", hour, minute, second);
        
        // 显示时间
        char time_str[16];
        snprintf(time_str, sizeof(time_str), "%02d:%02d:%02d", hour, minute, second);
        printf("时间字符串: '%s'\r\n", time_str);
        OLED_ShowStr(0, 0, time_str, 8);
        
        // 获取电压
        float adc_voltage = get_channel_voltage(0);
        float voltage = adc_voltage * current_ratio;
        printf("ADC电压: %.3f V, 变比: %.2f, 实际电压: %.3f V\r\n", 
               adc_voltage, current_ratio, voltage);
        
        // 显示电压
        char voltage_str[16];
        snprintf(voltage_str, sizeof(voltage_str), "%.2f V", voltage);
        printf("电压字符串: '%s'\r\n", voltage_str);
        OLED_ShowStr(0, 2, voltage_str, 8);
        
        printf("OLED应该显示:\r\n");
        printf("  第一行: %s\r\n", time_str);
        printf("  第二行: %s\r\n", voltage_str);
        
    } else {
        printf("进入idle状态显示分支\r\n");
        OLED_ShowStr(0, 0, "system idle", 8);
        OLED_ShowStr(0, 2, "           ", 8);
        printf("OLED应该显示:\r\n");
        printf("  第一行: system idle\r\n");
        printf("  第二行: (空)\r\n");
    }
    
    printf("\r\n=== 调试更新完成 ===\r\n");
}

/*!
    \brief      检查OLED更新调用
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_oled_update_calls(void)
{
    printf("\r\n=== 检查OLED更新调用 ===\r\n");
    
    printf("🔍 OLED更新调用点:\r\n");
    printf("1. start_periodic_sampling() → update_oled_display()\r\n");
    printf("2. stop_periodic_sampling() → update_oled_display()\r\n");
    printf("3. sampling_task() → update_oled_display() (每秒)\r\n");
    printf("4. btn_task() → update_oled_display() (按键时)\r\n");
    
    printf("\r\n🎯 关键检查点:\r\n");
    printf("1. 确认sampling_task()在主循环中被调用\r\n");
    printf("2. 确认主循环有1秒延时\r\n");
    printf("3. 确认uart_flag和sampling_active状态正确\r\n");
    printf("4. 确认RTC时间读取正常\r\n");
    printf("5. 确认ADC电压读取正常\r\n");
    
    printf("\r\n📋 建议的主循环结构:\r\n");
    printf("while(1) {\r\n");
    printf("    uart_task();        // 处理串口命令\r\n");
    printf("    btn_task();         // 处理按键\r\n");
    printf("    sampling_task();    // 采样任务，包含OLED更新\r\n");
    printf("    led_task();         // LED任务\r\n");
    printf("    delay_1s();         // 1秒延时\r\n");
    printf("}\r\n");
    
    printf("\r\n=== 检查完成 ===\r\n");
}

/*!
    \brief      OLED显示故障排除
    \param[in]  none
    \param[out] none
    \retval     none
*/
void oled_display_troubleshooting(void)
{
    printf("\r\n=== OLED显示故障排除 ===\r\n");
    
    printf("❓ 如果OLED没有显示时间和电压:\r\n");
    
    printf("\r\n1. 检查硬件连接:\r\n");
    printf("   - 确认OLED电源连接正常\r\n");
    printf("   - 确认I2C连接正常 (SDA/SCL)\r\n");
    printf("   - 确认OLED地址配置正确\r\n");
    
    printf("\r\n2. 检查OLED初始化:\r\n");
    printf("   - 确认OLED_Init()已调用\r\n");
    printf("   - 确认I2C初始化正常\r\n");
    printf("   - 测试OLED_ShowStr()基础功能\r\n");
    
    printf("\r\n3. 检查函数调用:\r\n");
    printf("   - 在update_oled_display()开头添加printf调试\r\n");
    printf("   - 确认函数被正确调用\r\n");
    printf("   - 确认条件判断正确\r\n");
    
    printf("\r\n4. 检查数据获取:\r\n");
    printf("   - 确认RTC时间读取正常\r\n");
    printf("   - 确认ADC电压读取正常\r\n");
    printf("   - 确认字符串格式化正确\r\n");
    
    printf("\r\n5. 分步调试:\r\n");
    printf("   - 先测试固定字符串显示\r\n");
    printf("   - 再测试时间显示\r\n");
    printf("   - 最后测试电压显示\r\n");
    printf("   - 逐步排查问题环节\r\n");
    
    printf("\r\n=== 故障排除完成 ===\r\n");
}

/*!
    \brief      OLED显示修复建议
    \param[in]  none
    \param[out] none
    \retval     none
*/
void oled_display_fix_suggestions(void)
{
    printf("\r\n=== OLED显示修复建议 ===\r\n");
    
    printf("🔧 立即可尝试的修复:\r\n");
    
    printf("\r\n1. 添加调试输出:\r\n");
    printf("在update_oled_display()函数开头添加:\r\n");
    printf("printf(\"OLED更新: uart_flag=%%d, sampling_active=%%d\\r\\n\", uart_flag, sampling_active);\r\n");
    
    printf("\r\n2. 强制OLED更新:\r\n");
    printf("在start命令处理后立即调用:\r\n");
    printf("debug_update_oled_display();  // 使用调试版本\r\n");
    
    printf("\r\n3. 检查变量状态:\r\n");
    printf("确认发送start后:\r\n");
    printf("uart_flag = 1\r\n");
    printf("sampling_active = 1\r\n");
    
    printf("\r\n4. 简化测试:\r\n");
    printf("临时在start命令处理后直接调用:\r\n");
    printf("OLED_ShowStr(0, 0, \"12:34:56\", 8);\r\n");
    printf("OLED_ShowStr(0, 2, \"10.50 V\", 8);\r\n");
    
    printf("\r\n5. 检查主循环:\r\n");
    printf("确认sampling_task()每秒被调用\r\n");
    printf("确认没有其他代码覆盖OLED显示\r\n");
    
    printf("\r\n=== 修复建议完成 ===\r\n");
}

/*!
    \brief      OLED显示格式修复完整验证
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_oled_display_format_fix(void)
{
    printf("\r\n========================================\r\n");
    printf("🔧 OLED显示格式修复完整验证\r\n");
    printf("========================================\r\n");
    
    printf("🎯 宝宝的要求:\r\n");
    printf("发送start后，OLED应该显示:\r\n");
    printf("  第一行: 00:30:05 (时分秒格式)\r\n");
    printf("  第二行: 10.50 V (两位小数格式)\r\n");
    
    printf("\r\n📋 代码格式检查:\r\n");
    printf("✅ 时间格式代码正确: %%02d:%%02d:%%02d\r\n");
    printf("✅ 电压格式代码正确: %%.2f V\r\n");
    printf("✅ 条件判断正确: uart_flag && sampling_active\r\n");
    
    printf("\r\n🔍 可能的问题:\r\n");
    printf("1. OLED硬件或初始化问题\r\n");
    printf("2. update_oled_display()没有被调用\r\n");
    printf("3. 状态变量不正确\r\n");
    printf("4. 主循环调用问题\r\n");
    
    // 执行所有测试和检查
    oled_display_format_problem_analysis();
    test_oled_display_step_by_step();
    debug_update_oled_display();
    check_oled_update_calls();
    oled_display_troubleshooting();
    oled_display_fix_suggestions();
    
    printf("\r\n========================================\r\n");
    printf("🎉 请按照建议逐步排查OLED显示问题！\r\n");
    printf("代码格式是正确的，问题可能在调用或硬件！\r\n");
    printf("========================================\r\n");
}
