# RTC功能使用指南

## 🎯 题目要求

### 2.1 串口输入"RTC Config"，串口返回"Input Datetime"
### 2.2 输入当前标准时间，例如"2025-01-01 15:00:10"，返回指定格式
### 2.3 输入"RTC now"，串口返回当前时间

## ✅ 你的底层代码状态

### 已完成的功能：
- ✅ 串口DMA接收配置完整
- ✅ USART0中断处理正确
- ✅ RTC初始化函数完整
- ✅ 调度器包含uart_task
- ✅ 命令处理函数已实现
- ✅ RTC时间设置和读取函数已实现

### 代码文件状态：
- `sysFunction/usart_app.c` - 主要RTC功能实现 ✅
- `Components/bsp/mcu_cmic_gd32f470vet6.c` - 底层初始化 ✅
- `USER/src/gd32f4xx_it.c` - 中断处理 ✅
- `sysFunction/scheduler.c` - 任务调度 ✅

## 🔧 测试步骤

### 步骤1：编译和烧录
1. 确保代码已编译成功（0错误0警告）
2. 烧录到目标板
3. 连接串口调试工具（115200波特率）

### 步骤2：基本功能测试
```
发送: RTC Config
期望: Input Datetime

发送: 2025-01-01 15:00:10
期望: RTC Config success         Time:2025-01-01 15:00:10

发送: RTC now
期望: Current Time:2025-01-01 15:00:XX
```

### 步骤3：多种格式测试
```
发送: RTC Config
期望: Input Datetime

发送: 2025-12-25 23:59:59
期望: RTC Config success         Time:2025-12-25 23:59:59

发送: RTC now
期望: Current Time:2025-12-25 23:59:XX
```

## 🐛 如果遇到问题

### 问题1：没有串口输出
**检查：**
- 串口连接是否正确
- 波特率是否为115200
- 系统是否正常启动

**解决：**
- 检查串口线连接
- 确认串口工具设置
- 重新上电复位

### 问题2：发送命令无响应
**检查：**
- 是否发送了回车换行符
- 命令格式是否正确
- uart_task是否在运行

**解决：**
- 确保发送"RTC Config\r\n"
- 检查调度器是否正常运行
- 添加调试输出

### 问题3：时间设置失败
**检查：**
- RTC时钟源是否配置正确
- 时间格式是否正确
- RTC初始化是否成功

**解决：**
- 检查32.768kHz晶振
- 尝试使用内部时钟源
- 检查RTC寄存器状态

## 📊 调试方法

### 方法1：添加调试输出
在关键位置添加printf输出：
```c
void uart_task(void)
{
    if(!rx_flag) return;
    
    printf("DEBUG: 收到命令: %s\r\n", rxbuffer);  // 调试输出
    
    rxbuffer[511] = '\0';
    process_uart_command((char*)rxbuffer);
    
    memset(rxbuffer, 0, 512);
    rx_flag = 0;
}
```

### 方法2：检查系统状态
```c
// 在main函数中添加
printf("系统启动完成\r\n");
printf("RTC初始化状态: %d\r\n", bsp_rtc_init());
printf("串口初始化完成\r\n");
```

### 方法3：使用测试函数
```c
// 在main函数中添加
#include "RTC_Final_Test.c"

int main(void)
{
    // ... 原有初始化代码 ...
    
    // 添加RTC测试
    rtc_system_diagnosis();
    
    // ... 原有主循环 ...
}
```

## 🎯 预期结果

### 正常工作时的输出：
```
====system init====
Device_ID:2025-CIMC-137766
====system ready====

> RTC Config
Input Datetime

> 2025-01-01 15:00:10
RTC Config success         Time:2025-01-01 15:00:10

> RTC now
Current Time:2025-01-01 15:00:15
```

## 📝 代码关键点

### 1. 串口接收处理
```c
void uart_task(void)
{
    if(!rx_flag) return;
    rxbuffer[511] = '\0';  // 安全保护
    process_uart_command((char*)rxbuffer);
    memset(rxbuffer, 0, 512);
    rx_flag = 0;
}
```

### 2. 命令处理逻辑
```c
void process_uart_command(char* command)
{
    // 清理字符串
    char* pos = strchr(command, '\r');
    if (pos) *pos = '\0';
    pos = strchr(command, '\n');
    if (pos) *pos = '\0';
    
    // 处理RTC配置模式
    if (rtc_config_mode) {
        process_rtc_time_input(command);
        rtc_config_mode = 0;
        return;
    }
    
    // 处理命令
    if (strcmp(command, "RTC Config") == 0) {
        rtc_config_mode = 1;
        my_printf(DEBUG_USART, "Input Datetime\r\n");
    } else if (strcmp(command, "RTC now") == 0) {
        show_current_time();
    }
}
```

### 3. RTC时间设置
```c
void process_rtc_time_input(char* time_str)
{
    int year, month, day, hour, minute, second;
    int parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", 
                       &year, &month, &day, &hour, &minute, &second);
    
    if (parsed == 6) {
        // 设置RTC时间
        rtc_time.year = decimal_to_bcd(year - 2000);
        rtc_time.month = month_to_rtc_enum(month);
        rtc_time.date = decimal_to_bcd(day);
        rtc_time.hour = decimal_to_bcd(hour);
        rtc_time.minute = decimal_to_bcd(minute);
        rtc_time.second = decimal_to_bcd(second);
        
        if (rtc_init(&rtc_time) == SUCCESS) {
            my_printf(DEBUG_USART, "RTC Config success         Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                      year, month, day, hour, minute, second);
        }
    }
}
```

## 🚀 快速验证

你的代码已经完整实现了所有要求的功能！现在只需要：

1. **编译烧录** - 代码已经编译成功
2. **连接串口** - 115200波特率
3. **发送测试命令** - 按照上面的测试步骤
4. **验证输出格式** - 确保与题目要求一致

你的底层实现非常完整，应该能够完美满足题目要求！🎉
