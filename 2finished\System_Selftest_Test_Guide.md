# 系统自检功能测试指南

## 功能概述
通过串口输入"test"指令，触发系统自检，并返回格式化的自检结果。

## 测试步骤

### 1. 硬件连接
- 确保串口连接正常（USART0，波特率115200）
- 确保SPI Flash已正确连接
- 可选：插入TF卡进行完整测试

### 2. 软件测试

#### 测试命令
```
test
```

#### 预期输出格式（全部通过）
```
====== system selftest======
flash....................ok
TF card..................ok
flash ID:0xC84017
TF card memory: 7580 KB
RTC:2025-01-01 01:00:50
====== system selftest======
```

#### 预期输出格式（TF卡未插入）
```
====== system selftest======
flash....................ok
TF card..................error
flash ID:0xC84017
can not find TF card
RTC:2025-01-01 01:00:50
====== system selftest======
```

#### 预期输出格式（Flash异常）
```
====== system selftest======
flash....................error
TF card..................ok
can not read flash ID
TF card memory: 7580 KB
RTC:2025-01-01 01:00:50
====== system selftest======
```

## 测试项目详解

### 1. Flash测试
- **测试方法**: 调用`spi_flash_read_id()`读取Flash ID
- **通过条件**: ID不为0x000000且不为0xFFFFFF
- **输出**: 显示实际读取的Flash ID（如0xC84017表示GD25Q64）

### 2. TF卡测试
- **测试方法**: 调用`sd_init()`初始化SD卡
- **通过条件**: SD卡初始化成功
- **输出**: 显示SD卡容量（KB为单位）

### 3. RTC测试
- **测试方法**: 调用`rtc_current_time_get()`读取当前时间
- **通过条件**: 时、分、秒在有效范围内
- **输出**: 显示当前RTC时间（格式：YYYY-MM-DD HH:MM:SS）

## 故障排除

### Flash测试失败
1. 检查SPI1接线是否正确
2. 检查Flash芯片是否正常供电
3. 检查CS引脚（PB12）是否正常

### TF卡测试失败
1. 检查TF卡是否正确插入
2. 检查SDIO接线是否正确
3. 尝试更换TF卡

### RTC测试失败
1. 检查RTC时钟源配置
2. 检查RTC是否正确初始化
3. 检查备份电源（如果有）

## 代码集成说明

### 主要文件
- `sysFunction/system_selftest.c` - 自检功能实现
- `sysFunction/system_selftest.h` - 自检功能头文件
- `sysFunction/usart_app.c` - 串口命令处理

### 关键函数
- `system_selftest_init()` - 初始化自检模块
- `system_selftest_run()` - 执行完整自检
- `process_uart_command()` - 处理串口命令

### 调度器集成
自检功能已集成到主调度器中：
- 串口任务每5ms检查一次接收缓冲区
- 接收到"test"命令时立即执行自检
- 其他命令会返回"Unknown command"提示

## 扩展功能建议

可以根据需要添加更多自检项目：
- ADC功能测试
- DAC功能测试  
- LED功能测试
- 按键功能测试
- OLED显示测试

每个新增测试项目都应遵循相同的接口规范：
```c
selftest_result_t selftest_xxx(selftest_item_t* item);
```
