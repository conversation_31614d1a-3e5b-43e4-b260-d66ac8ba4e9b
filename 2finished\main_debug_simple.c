/* 
 * 简化版main.c - 用于调试上电无输出问题
 * 只包含最基本的功能，逐步排查问题
 */
#include "mcu_cmic_gd32f470vet6.h"

// RTC时间结构体
typedef struct {
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} rtc_time_t;

// 全局变量
static rtc_time_t g_rtc_time;
static uint8_t g_uart_rx_buffer[64];
static uint8_t g_uart_rx_count = 0;
static uint8_t g_waiting_for_datetime = 0;

// 函数声明
void rtc_init(void);
void rtc_set_time(rtc_time_t *time);
void rtc_get_time(rtc_time_t *time);
void parse_datetime_string(char *str, rtc_time_t *time);
void process_uart_command(void);

int main(void)
{
    // 基础系统初始化
    systick_config();
    init_cycle_counter(false);
    delay_ms(200);
    
    // LED指示 - 确认程序运行
    bsp_led_init();
    
    // 初始化串口
    bsp_usart_init();
    delay_ms(500);
    
    // 初始化RTC
    rtc_init();
    
    // 初始化OLED
    bsp_oled_init();
    delay_ms(100);
    OLED_Init();
    delay_ms(100);
    
    // 显示初始状态
    OLED_Clear();
    OLED_ShowStr(0, 0, "RTC Ready", 8);
    
    // 主循环
    while(1) {
        // 处理串口命令
        process_uart_command();
        
        // LED心跳
        LED1_TOGGLE();
        delay_ms(500);
    }
}

// RTC初始化
void rtc_init(void)
{
    // 使能RTC时钟
    rcu_osci_on(RCU_IRC40K);
    while(rcu_osci_stab_wait(RCU_IRC40K) != SUCCESS);
    
    // 配置RTC
    rtc_parameter_struct rtc_init_struct;
    rtc_init_struct.rtc_factor_asyn = 0x7F;
    rtc_init_struct.rtc_factor_syn = 0x00FF;
    rtc_init_struct.rtc_alarm_day = 0x01;
    rtc_init_struct.rtc_weekday = 0x01;
    rtc_init_struct.rtc_hour = 0x00;
    rtc_init_struct.rtc_minute = 0x00;
    rtc_init_struct.rtc_second = 0x00;
    rtc_init_struct.rtc_year = 0x2025;
    rtc_init_struct.rtc_month = 0x01;
    rtc_init_struct.rtc_date = 0x01;
    
    rtc_init(&rtc_init_struct);
    rtc_register_sync_wait();
}

// 设置RTC时间
void rtc_set_time(rtc_time_t *time)
{
    rtc_parameter_struct rtc_init_struct;
    rtc_init_struct.rtc_year = time->year;
    rtc_init_struct.rtc_month = time->month;
    rtc_init_struct.rtc_date = time->day;
    rtc_init_struct.rtc_hour = time->hour;
    rtc_init_struct.rtc_minute = time->minute;
    rtc_init_struct.rtc_second = time->second;
    
    rtc_init(&rtc_init_struct);
    rtc_register_sync_wait();
}

// 获取RTC时间
void rtc_get_time(rtc_time_t *time)
{
    rtc_parameter_struct rtc_init_struct;
    rtc_init(&rtc_init_struct);
    
    time->year = rtc_init_struct.rtc_year;
    time->month = rtc_init_struct.rtc_month;
    time->day = rtc_init_struct.rtc_date;
    time->hour = rtc_init_struct.rtc_hour;
    time->minute = rtc_init_struct.rtc_minute;
    time->second = rtc_init_struct.rtc_second;
}

// 解析日期时间字符串
void parse_datetime_string(char *str, rtc_time_t *time)
{
    char *token;
    char *delim = " -:";
    int values[6] = {0};
    int i = 0;
    
    token = strtok(str, delim);
    while(token != NULL && i < 6) {
        values[i++] = atoi(token);
        token = strtok(NULL, delim);
    }
    
    if(i == 6) {
        time->year = values[0];
        time->month = values[1];
        time->day = values[2];
        time->hour = values[3];
        time->minute = values[4];
        time->second = values[5];
    }
}

// 处理串口命令
void process_uart_command(void)
{
    if(usart_flag_get(USART0, USART_FLAG_RBNE)) {
        uint8_t ch = usart_data_receive(USART0);
        
        if(ch == '\r' || ch == '\n') {
            if(g_uart_rx_count > 0) {
                g_uart_rx_buffer[g_uart_rx_count] = '\0';
                
                if(strcmp((char*)g_uart_rx_buffer, "RTC Config") == 0) {
                    my_printf(DEBUG_USART, "Input Datetime\r\n");
                    g_waiting_for_datetime = 1;
                }
                else if(strcmp((char*)g_uart_rx_buffer, "RTC now") == 0) {
                    rtc_time_t current_time;
                    rtc_get_time(&current_time);
                    my_printf(DEBUG_USART, "Current Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                             current_time.year, current_time.month, current_time.day,
                             current_time.hour, current_time.minute, current_time.second);
                }
                else if(g_waiting_for_datetime) {
                    rtc_time_t new_time;
                    parse_datetime_string((char*)g_uart_rx_buffer, &new_time);
                    rtc_set_time(&new_time);
                    my_printf(DEBUG_USART, "RTC Config success         Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                             new_time.year, new_time.month, new_time.day,
                             new_time.hour, new_time.minute, new_time.second);
                    g_waiting_for_datetime = 0;
                }
                
                g_uart_rx_count = 0;
            }
        }
        else if(g_uart_rx_count < sizeof(g_uart_rx_buffer) - 1) {
            g_uart_rx_buffer[g_uart_rx_count++] = ch;
        }
    }
}

/* 如果需要使用这个简化版本进行调试：
 * 1. 备份当前的main.c
 * 2. 将此文件内容复制到main.c
 * 3. 编译并测试
 * 4. 观察LED和串口输出
 * 5. 确认问题后恢复原main.c
 */
