/*
 * RTC简单测试程序
 * 用于验证RTC基本功能是否正常工作
 */

#include "mcu_cmic_gd32f470vet6.h"

// 简化的RTC测试函数
void rtc_simple_test(void)
{
    rtc_parameter_struct rtc_time;
    
    printf("\r\n=== RTC简单测试开始 ===\r\n");
    
    // 测试1：设置一个固定时间
    printf("1. 设置时间为: 2025-06-15 14:30:25\r\n");
    
    rtc_time.year = 0x25;           // 2025年的后两位，BCD格式
    rtc_time.month = RTC_JUN;       // 6月
    rtc_time.date = 0x15;           // 15日，BCD格式
    rtc_time.hour = 0x14;           // 14时，BCD格式
    rtc_time.minute = 0x30;         // 30分，BCD格式
    rtc_time.second = 0x25;         // 25秒，BCD格式
    rtc_time.day_of_week = RTC_SATURDAY;
    rtc_time.display_format = RTC_24HOUR;
    rtc_time.am_pm = RTC_AM;
    rtc_time.factor_asyn = 0x7F;    // 预分频器
    rtc_time.factor_syn = 0xFF;
    
    if (rtc_init(&rtc_time) == SUCCESS) {
        printf("   ✓ RTC时间设置成功\r\n");
    } else {
        printf("   ✗ RTC时间设置失败\r\n");
        return;
    }
    
    // 等待一小段时间
    for(volatile int i = 0; i < 1000000; i++);
    
    // 测试2：读取时间
    printf("2. 读取当前时间:\r\n");
    rtc_current_time_get(&rtc_time);
    
    // 转换并显示
    int year = 2000 + ((rtc_time.year >> 4) * 10 + (rtc_time.year & 0x0F));
    int month = 6; // 固定为6月，因为我们设置的是RTC_JUN
    int day = (rtc_time.date >> 4) * 10 + (rtc_time.date & 0x0F);
    int hour = (rtc_time.hour >> 4) * 10 + (rtc_time.hour & 0x0F);
    int minute = (rtc_time.minute >> 4) * 10 + (rtc_time.minute & 0x0F);
    int second = (rtc_time.second >> 4) * 10 + (rtc_time.second & 0x0F);
    
    printf("   当前时间: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           year, month, day, hour, minute, second);
    
    // 测试3：等待几秒看时间是否在走
    printf("3. 等待5秒，观察时间变化:\r\n");
    for(int i = 0; i < 5; i++) {
        // 等待1秒
        for(volatile int j = 0; j < 2000000; j++);
        
        // 读取时间
        rtc_current_time_get(&rtc_time);
        second = (rtc_time.second >> 4) * 10 + (rtc_time.second & 0x0F);
        printf("   第%d秒: %02d\r\n", i+1, second);
    }
    
    printf("=== RTC测试完成 ===\r\n\r\n");
}

// 检查RTC寄存器状态
void rtc_register_check(void)
{
    printf("\r\n=== RTC寄存器状态检查 ===\r\n");
    printf("RTC_TIME: 0x%08X\r\n", RTC_TIME);
    printf("RTC_DATE: 0x%08X\r\n", RTC_DATE);
    printf("RTC_CTL:  0x%08X\r\n", RTC_CTL);
    printf("RTC_STAT: 0x%08X\r\n", RTC_STAT);
    printf("RTC_PSC:  0x%08X\r\n", RTC_PSC);
    printf("========================\r\n\r\n");
}

// 检查时钟源配置
void rtc_clock_source_check(void)
{
    printf("\r\n=== RTC时钟源检查 ===\r\n");
    
    uint32_t bdctl = RCU_BDCTL;
    uint32_t rtc_src = (bdctl >> 8) & 0x3;
    
    printf("RCU_BDCTL: 0x%08X\r\n", bdctl);
    printf("RTC时钟源: ");
    
    switch(rtc_src) {
        case 0:
            printf("无时钟源\r\n");
            break;
        case 1:
            printf("LXTAL (32.768kHz外部晶振)\r\n");
            break;
        case 2:
            printf("IRC32K (内部32kHz振荡器)\r\n");
            break;
        case 3:
            printf("HXTAL/32 (外部高速晶振32分频)\r\n");
            break;
    }
    
    // 检查LXTAL状态
    if (rtc_src == 1) {
        if (RCU_BDCTL & RCU_BDCTL_LXTALSTB) {
            printf("LXTAL状态: 稳定\r\n");
        } else {
            printf("LXTAL状态: 不稳定 ⚠️\r\n");
        }
    }
    
    printf("==================\r\n\r\n");
}

// 完整的RTC诊断
void rtc_full_diagnosis(void)
{
    printf("\r\n========== RTC完整诊断 ==========\r\n");
    
    // 1. 检查时钟源
    rtc_clock_source_check();
    
    // 2. 检查寄存器
    rtc_register_check();
    
    // 3. 功能测试
    rtc_simple_test();
    
    printf("========== 诊断完成 ==========\r\n\r\n");
}

/*
 * 使用方法：
 * 
 * 在main函数中调用：
 * 1. bsp_rtc_init();           // 初始化RTC
 * 2. rtc_full_diagnosis();     // 运行完整诊断
 * 
 * 或者单独测试：
 * - rtc_simple_test();         // 基本功能测试
 * - rtc_register_check();      // 寄存器检查
 * - rtc_clock_source_check();  // 时钟源检查
 */
