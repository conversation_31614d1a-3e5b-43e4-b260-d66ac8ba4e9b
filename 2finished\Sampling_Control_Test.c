/*
 * 采样控制功能完整测试程序
 * 验证串口控制、按键控制、周期调整和OLED显示
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试采样控制的完整流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_sampling_control_flow(void)
{
    printf("\r\n=== 采样控制完整流程测试 ===\r\n");
    
    printf("题目要求验证：\r\n");
    printf("1. 串口控制：start/stop命令\r\n");
    printf("2. 按键控制：KEY1启停，KEY2/3/4调整周期\r\n");
    printf("3. LED指示：采样时LED1闪烁(1s周期)\r\n");
    printf("4. OLED显示：时间和电压值\r\n");
    printf("5. 电压范围：0-3.3V，保留两位小数\r\n");
    printf("6. 周期可选：5s/10s/15s\r\n");
    printf("\r\n");
}

/*!
    \brief      测试串口控制功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_uart_sampling_control(void)
{
    printf("\r\n=== 串口采样控制测试 ===\r\n");
    
    printf("1. 测试start命令：\r\n");
    printf("期望输出:\r\n");
    printf("  Periodic Sampling\r\n");
    printf("  sample cycle: 5s\r\n");
    printf("  2025-01-01 00:30:05 ch0=x.xxV\r\n");
    printf("  ...\r\n");
    printf("\r\n实际输出:\r\n");
    
    // 模拟start命令
    process_uart_command("start");
    
    printf("\r\n2. 等待几秒观察采样输出...\r\n");
    
    // 模拟几次采样
    for (int i = 0; i < 3; i++) {
        // 模拟时间流逝
        static uint32_t sim_time = 0;
        sim_time += 5;  // 每次增加5秒
        sampling_task(sim_time);
        
        // 短暂延时
        for(volatile int j = 0; j < 1000000; j++);
    }
    
    printf("\r\n3. 测试stop命令：\r\n");
    printf("期望输出: Periodic Sampling STOP\r\n");
    printf("实际输出: ");
    
    // 模拟stop命令
    process_uart_command("stop");
    
    printf("\r\n=== 串口控制测试完成 ===\r\n");
}

/*!
    \brief      测试按键控制功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_key_sampling_control(void)
{
    printf("\r\n=== 按键采样控制测试 ===\r\n");
    
    printf("1. 模拟KEY1按下（启停控制）：\r\n");
    toggle_sampling_state();
    printf("采样状态已切换\r\n");
    
    printf("\r\n2. 模拟KEY2按下（5s周期）：\r\n");
    set_sample_cycle(5);
    
    printf("\r\n3. 模拟KEY3按下（10s周期）：\r\n");
    set_sample_cycle(10);
    
    printf("\r\n4. 模拟KEY4按下（15s周期）：\r\n");
    set_sample_cycle(15);
    
    printf("\r\n5. 再次模拟KEY1按下（停止采样）：\r\n");
    toggle_sampling_state();
    
    printf("\r\n=== 按键控制测试完成 ===\r\n");
}

/*!
    \brief      测试电压采样范围
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_voltage_sampling(void)
{
    printf("\r\n=== 电压采样测试 ===\r\n");
    
    printf("电压范围：0-3.3V，保留两位小数\r\n");
    printf("采样10次电压值：\r\n");
    
    for (int i = 0; i < 10; i++) {
        float voltage = get_channel_voltage(0);
        printf("第%d次采样: %.2fV\r\n", i+1, voltage);
        
        // 验证范围
        if (voltage < 0.0f || voltage > 3.3f) {
            printf("  ⚠️ 电压超出范围！\r\n");
        } else {
            printf("  ✓ 电压在正常范围内\r\n");
        }
    }
    
    printf("\r\n=== 电压采样测试完成 ===\r\n");
}

/*!
    \brief      测试OLED显示功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_oled_display(void)
{
    printf("\r\n=== OLED显示测试 ===\r\n");
    
    printf("1. 测试idle状态显示：\r\n");
    printf("第一行：system idle\r\n");
    printf("第二行：空\r\n");
    update_oled_display();  // 当前应该是停止状态
    
    printf("\r\n2. 启动采样，测试采样状态显示：\r\n");
    printf("第一行：hh:mm:ss (时间)\r\n");
    printf("第二行：xx.xx V (电压)\r\n");
    
    // 启动采样
    start_periodic_sampling();
    
    // 更新几次显示
    for (int i = 0; i < 3; i++) {
        printf("更新显示 %d:\r\n", i+1);
        update_oled_display();
        
        // 短暂延时
        for(volatile int j = 0; j < 500000; j++);
    }
    
    // 停止采样
    stop_periodic_sampling();
    
    printf("\r\n=== OLED显示测试完成 ===\r\n");
}

/*!
    \brief      测试周期调整功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_cycle_adjustment(void)
{
    printf("\r\n=== 周期调整测试 ===\r\n");
    
    printf("测试不同采样周期：\r\n");
    
    // 测试5s周期
    printf("\r\n1. 设置5s周期：\r\n");
    set_sample_cycle(5);
    start_periodic_sampling();
    
    // 模拟采样
    for (int i = 0; i < 2; i++) {
        static uint32_t time_5s = 0;
        time_5s += 5;
        sampling_task(time_5s);
    }
    
    // 测试10s周期
    printf("\r\n2. 设置10s周期：\r\n");
    set_sample_cycle(10);
    
    // 模拟采样
    for (int i = 0; i < 2; i++) {
        static uint32_t time_10s = 0;
        time_10s += 10;
        sampling_task(time_10s);
    }
    
    // 测试15s周期
    printf("\r\n3. 设置15s周期：\r\n");
    set_sample_cycle(15);
    
    // 模拟采样
    for (int i = 0; i < 2; i++) {
        static uint32_t time_15s = 0;
        time_15s += 15;
        sampling_task(time_15s);
    }
    
    stop_periodic_sampling();
    
    printf("\r\n=== 周期调整测试完成 ===\r\n");
}

/*!
    \brief      验证输出格式
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_output_format(void)
{
    printf("\r\n=== 验证输出格式 ===\r\n");
    
    printf("题目要求的输出格式：\r\n");
    printf("启动采样:\r\n");
    printf("  输入: start\r\n");
    printf("  输出: Periodic Sampling\r\n");
    printf("        sample cycle: 5s\r\n");
    printf("        2025-01-01 00:30:05 ch0=x.xxV\r\n");
    printf("        2025-01-01 00:30:10 ch0=x.xxV\r\n");
    printf("\r\n");
    printf("停止采样:\r\n");
    printf("  输入: stop\r\n");
    printf("  输出: Periodic Sampling STOP\r\n");
    printf("\r\n");
    printf("周期调整:\r\n");
    printf("  按KEY3: sample cycle adjust: 10s\r\n");
    printf("          2025-01-01 00:30:05 ch0=x.xxV\r\n");
    printf("          2025-01-01 00:30:15 ch0=x.xxV\r\n");
    printf("\r\n");
    
    printf("实际测试输出格式：\r\n");
    
    // 测试实际输出
    process_uart_command("start");
    
    // 模拟一次采样
    sampling_task(5);
    
    process_uart_command("stop");
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*!
    \brief      完整的采样控制测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_sampling_control_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        采样控制功能完整测试          #\r\n");
    printf("##########################################\r\n");
    
    // 1. 显示测试说明
    test_sampling_control_flow();
    
    // 2. 串口控制测试
    test_uart_sampling_control();
    
    // 3. 按键控制测试
    test_key_sampling_control();
    
    // 4. 电压采样测试
    test_voltage_sampling();
    
    // 5. OLED显示测试
    test_oled_display();
    
    // 6. 周期调整测试
    test_cycle_adjustment();
    
    // 7. 输出格式验证
    verify_output_format();
    
    printf("\r\n##########################################\r\n");
    printf("#        测试完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n现在可以手动测试：\r\n");
    printf("串口命令：\r\n");
    printf("  - 发送 'start' 启动采样\r\n");
    printf("  - 发送 'stop' 停止采样\r\n");
    printf("\r\n");
    printf("按键操作：\r\n");
    printf("  - KEY1: 启停控制\r\n");
    printf("  - KEY2: 5s周期\r\n");
    printf("  - KEY3: 10s周期\r\n");
    printf("  - KEY4: 15s周期\r\n");
    printf("\r\n");
    printf("观察项目：\r\n");
    printf("  - LED1闪烁(采样时)\r\n");
    printf("  - OLED显示时间和电压\r\n");
    printf("  - 串口输出采样数据\r\n");
    printf("  - 电压范围0-3.3V\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_sampling_control_test();
 * 
 * 这会测试采样控制的所有功能：
 * 1. 串口start/stop命令
 * 2. 按键控制功能
 * 3. 电压采样(0-3.3V)
 * 4. OLED显示
 * 5. 周期调整(5s/10s/15s)
 * 6. 输出格式验证
 * 
 * 期望的完整交互：
 * 
 * 输入: start
 * 输出: Periodic Sampling
 *       sample cycle: 5s
 *       2025-01-01 00:30:05 ch0=1.65V
 *       2025-01-01 00:30:10 ch0=1.72V
 *       ...
 * 
 * 输入: stop
 * 输出: Periodic Sampling STOP
 * 
 * 按KEY3:
 * 输出: sample cycle adjust: 10s
 *       2025-01-01 00:30:05 ch0=1.58V
 *       2025-01-01 00:30:15 ch0=1.63V
 *       ...
 */
