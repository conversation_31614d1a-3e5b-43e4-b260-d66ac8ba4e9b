/*!
    \file    system_selftest.h
    \brief   system self-test module header file
    
    \version 2025-06-15, V1.0.0, system self-test for GD32F470VET6
*/

/*
    Copyright (c) 2025, MCUSTUDIO

    All rights reserved.
    Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors
       may be used to endorse or promote products derived from this software without
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
*/

#ifndef SYSTEM_SELFTEST_H
#define SYSTEM_SELFTEST_H

#include "stdint.h"
#include "stdbool.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 自检结果枚举 */
typedef enum {
    SELFTEST_OK = 0,
    SELFTEST_ERROR = 1
} selftest_result_t;

/* 自检项目结构体 */
typedef struct {
    char name[32];              /* 自检项目名称 */
    selftest_result_t result;   /* 自检结果 */
    char detail[128];           /* 详细信息 */
} selftest_item_t;

/* 自检报告结构体 */
typedef struct {
    selftest_item_t flash_test;     /* Flash测试 */
    selftest_item_t tfcard_test;    /* TF卡测试 */
    selftest_item_t rtc_test;       /* RTC测试 */
    uint32_t test_count;            /* 测试项目数量 */
    uint32_t pass_count;            /* 通过项目数量 */
} selftest_report_t;

/* 函数声明 */
void system_selftest_init(void);
void system_selftest_run(void);
selftest_result_t selftest_flash(selftest_item_t* item);
selftest_result_t selftest_tfcard(selftest_item_t* item);
selftest_result_t selftest_rtc(selftest_item_t* item);
void selftest_print_report(const selftest_report_t* report);

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_SELFTEST_H */
