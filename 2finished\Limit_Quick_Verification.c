/*
 * 阈值设置功能快速验证程序
 * 用于快速测试和验证阈值设置功能
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      快速验证阈值设置功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void quick_verify_limit_function(void)
{
    printf("\r\n=== 阈值设置功能快速验证 ===\r\n");
    
    printf("题目要求验证：\r\n");
    printf("1. 输入 'limit' 命令\r\n");
    printf("2. 显示当前阈值和输入提示\r\n");
    printf("3. 输入新阈值 (0-500范围)\r\n");
    printf("4. 成功时显示成功消息和新值\r\n");
    printf("5. 失败时显示错误消息和原值\r\n");
    printf("\r\n");
    
    // 测试1：正常设置
    printf("测试1：正常设置阈值为50.12\r\n");
    printf("期望输出:\r\n");
    printf("  limit = 1.00\r\n");
    printf("  Input value(0~500):\r\n");
    printf("  limit modified success\r\n");
    printf("  limit = 50.12\r\n");
    printf("\r\n实际输出:\r\n");
    
    // 模拟limit命令
    process_uart_command("limit");
    
    // 短暂延时
    for(volatile int i = 0; i < 500000; i++);
    
    // 模拟输入50.12
    process_uart_command("50.12");
    
    // 验证当前阈值
    printf("验证：当前阈值 = %.2f (期望: 50.12)\r\n", get_current_limit());
    
    printf("\r\n");
    
    // 测试2：错误输入
    printf("测试2：错误输入510.12（超出范围）\r\n");
    printf("期望输出:\r\n");
    printf("  limit = 50.12\r\n");
    printf("  Input value(0~500):\r\n");
    printf("  limit invalid\r\n");
    printf("  limit = 50.12\r\n");
    printf("\r\n实际输出:\r\n");
    
    process_uart_command("limit");
    for(volatile int i = 0; i < 500000; i++);
    process_uart_command("510.12");
    
    printf("验证：阈值未改变 = %.2f (期望: 50.12)\r\n", get_current_limit());
    
    printf("\r\n=== 验证完成 ===\r\n");
    printf("现在可以手动通过串口发送 'limit' 命令进行测试\r\n");
}

/*!
    \brief      测试所有边界情况
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_all_boundary_cases(void)
{
    printf("\r\n=== 边界情况测试 ===\r\n");
    
    // 测试边界值：0.0
    printf("1. 测试最小值 0.0\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("0.0");
    printf("结果: %.2f\r\n\r\n", get_current_limit());
    
    // 测试边界值：500.0
    printf("2. 测试最大值 500.0\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("500.0");
    printf("结果: %.2f\r\n\r\n", get_current_limit());
    
    // 测试刚好超出：500.01
    printf("3. 测试刚好超出 500.01\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("500.01");
    printf("结果: %.2f (应该保持500.00)\r\n\r\n", get_current_limit());
    
    // 测试负值：-0.01
    printf("4. 测试负值 -0.01\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("-0.01");
    printf("结果: %.2f (应该保持500.00)\r\n\r\n", get_current_limit());
    
    // 测试非数字：abc
    printf("5. 测试非数字 abc\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("abc");
    printf("结果: %.2f (应该保持500.00)\r\n", get_current_limit());
    
    printf("=== 边界测试完成 ===\r\n");
}

/*!
    \brief      验证输出格式精确匹配
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_exact_output_format(void)
{
    printf("\r\n=== 验证输出格式精确匹配 ===\r\n");
    
    printf("题目要求的精确格式：\r\n");
    printf("正常情况:\r\n");
    printf("  输入: limit\r\n");
    printf("  输出: limit = x.xx\r\n");
    printf("        Input value(0~500):\r\n");
    printf("  输入: 50.12\r\n");
    printf("  输出: limit modified success\r\n");
    printf("        limit = 50.12\r\n");
    printf("\r\n");
    printf("错误情况:\r\n");
    printf("  输入: limit\r\n");
    printf("  输出: limit = x.xx\r\n");
    printf("        Input value(0~500):\r\n");
    printf("  输入: 510.12\r\n");
    printf("  输出: limit invalid\r\n");
    printf("        limit = x.xx\r\n");
    printf("\r\n");
    
    printf("实际测试结果：\r\n");
    
    // 设置一个已知值
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("25.5");
    
    printf("\r\n正常情况实际输出：\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("50.12");
    
    printf("\r\n错误情况实际输出：\r\n");
    process_uart_command("limit");
    for(volatile int i = 0; i < 300000; i++);
    process_uart_command("510.12");
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*!
    \brief      完整的快速验证程序
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_limit_quick_verification(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        阈值设置功能快速验证          #\r\n");
    printf("##########################################\r\n");
    
    // 1. 基本功能验证
    quick_verify_limit_function();
    
    // 2. 边界情况测试
    test_all_boundary_cases();
    
    // 3. 输出格式验证
    verify_exact_output_format();
    
    printf("\r\n##########################################\r\n");
    printf("#        快速验证完成                  #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n手动测试说明：\r\n");
    printf("1. 通过串口发送 'limit' 命令\r\n");
    printf("2. 观察输出是否符合题目要求\r\n");
    printf("3. 输入有效值测试成功情况\r\n");
    printf("4. 输入无效值测试错误处理\r\n");
    printf("5. 验证阈值范围：0-500\r\n");
}

// 快速测试宏
#define LIMIT_QUICK_TEST() do { \
    printf("\r\n=== 阈值快速测试 ===\r\n"); \
    printf("当前阈值: %.2f\r\n", get_current_limit()); \
    printf("请通过串口发送以下命令测试:\r\n"); \
    printf("1. limit -> 应显示当前值和输入提示\r\n"); \
    printf("2. 50.12 -> 应显示成功消息\r\n"); \
    printf("3. limit -> 应显示更新后的值\r\n"); \
    printf("4. 510.12 -> 应显示错误消息\r\n"); \
    printf("5. -10 -> 应显示错误消息\r\n"); \
    printf("6. abc -> 应显示错误消息\r\n"); \
    printf("========================\r\n"); \
} while(0)

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行验证：
 * 
 * 1. complete_limit_quick_verification();  // 完整快速验证
 * 2. quick_verify_limit_function();        // 基本功能验证
 * 3. test_all_boundary_cases();            // 边界情况测试
 * 4. LIMIT_QUICK_TEST();                   // 快速测试宏
 * 
 * 然后通过串口发送 "limit" 命令进行实际测试
 * 
 * 期望的交互完全符合题目要求：
 * 
 * 输入: limit
 * 输出: limit = 1.00
 *       Input value(0~500):
 * 
 * 输入: 50.12
 * 输出: limit modified success
 *       limit = 50.12
 * 
 * 输入: limit
 * 输出: limit = 50.12
 *       Input value(0~500):
 * 
 * 输入: 510.12
 * 输出: limit invalid
 *       limit = 50.12
 */
