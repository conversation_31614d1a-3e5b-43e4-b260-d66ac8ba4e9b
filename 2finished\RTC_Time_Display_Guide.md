# RTC时间显示功能使用指南

## 功能概述
实现了通过串口显示RTC时间的功能，支持两种显示方式：
1. **自动显示** - 每秒自动输出时间（通过调度器）
2. **命令显示** - 通过"time"命令手动查看时间

## 实现的功能

### 1. 自动时间显示
- **执行周期**: 每500ms检查一次
- **输出条件**: 只在秒数变化时输出，避免重复
- **输出格式**: `RTC Time: 2025-04-30 23:59:50`

### 2. 手动时间查询
- **触发命令**: `time`
- **输出格式**: 详细的时间信息显示

## 使用方法

### 方法1: 观察自动显示
1. 系统启动后，RTC任务会自动运行
2. 每当秒数变化时，串口会输出当前时间
3. 输出格式：
   ```
   RTC Time: 2025-04-30 23:59:50
   RTC Time: 2025-04-30 23:59:51
   RTC Time: 2025-04-30 23:59:52
   ```

### 方法2: 使用time命令
1. 打开串口调试工具（115200波特率）
2. 发送命令：`time`
3. 系统返回详细时间信息：
   ```
   ====== Current RTC Time ======
   Date: 2025-04-30 (Sat)
   Time: 23:59:50
   Format: 24-Hour
   ===============================
   ```

## 技术实现

### 1. 自动显示实现
```c
void rtc_task(void)
{
    static uint32_t last_second = 0xFF; // 用于检测秒数变化
    
    rtc_current_time_get(&rtc_initpara);
    
    // 只在秒数变化时才打印，避免频繁输出
    if (rtc_initpara.second != last_second) {
        last_second = rtc_initpara.second;
        
        // 格式化输出RTC时间
        my_printf(DEBUG_USART, "RTC Time: 20%02x-%02x-%02x %02x:%02x:%02x\r\n",
                  rtc_initpara.year, 
                  rtc_initpara.month, 
                  rtc_initpara.date,
                  rtc_initpara.hour, 
                  rtc_initpara.minute, 
                  rtc_initpara.second);
    }
}
```

### 2. 命令显示实现
```c
void show_rtc_time(void)
{
    rtc_parameter_struct rtc_time;
    const char* weekdays[] = {"", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};
    
    // 读取当前RTC时间
    rtc_current_time_get(&rtc_time);
    
    // 格式化输出详细时间信息
    my_printf(DEBUG_USART, "====== Current RTC Time ======\r\n");
    my_printf(DEBUG_USART, "Date: 20%02x-%02x-%02x (%s)\r\n", ...);
    my_printf(DEBUG_USART, "Time: %02x:%02x:%02x\r\n", ...);
    my_printf(DEBUG_USART, "Format: %s\r\n", ...);
    my_printf(DEBUG_USART, "===============================\r\n");
}
```

### 3. 命令处理集成
```c
void process_uart_command(char* command)
{
    if (strcmp(command, "test") == 0) {
        system_selftest_run();
    } else if (strcmp(command, "time") == 0) {
        show_rtc_time();
    } else {
        my_printf(DEBUG_USART, "Unknown command: %s\r\n", command);
    }
}
```

## 支持的命令

### 当前支持的串口命令
1. **test** - 执行系统自检
2. **time** - 显示当前RTC时间

### 命令使用示例
```
> test
====== system selftest======
flash....................ok
TF card..................ok
flash ID:0xC84017
TF card memory: 7580 KB
RTC:2025-04-30 23:59:50
====== system selftest======

> time
====== Current RTC Time ======
Date: 2025-04-30 (Sat)
Time: 23:59:50
Format: 24-Hour
===============================

> hello
Unknown command: hello
```

## 时间格式说明

### BCD格式
- RTC内部使用BCD（Binary-Coded Decimal）格式存储时间
- 显示时使用`%02x`格式化，直接显示BCD值
- 例如：BCD值0x25表示十进制25

### 星期显示
- 1=Monday, 2=Tuesday, ..., 7=Sunday
- 使用字符串数组进行映射显示

### 时间格式
- 支持24小时制和12小时制
- 当前配置为24小时制显示

## 调度器集成

### 任务配置
```c
static task_t scheduler_task[] = {
    {led_task,  1,    0},    // LED任务，1ms周期
    {key_task,  1,    0},    // 按键任务，1ms周期
    {adc_task,  100,  0},    // ADC任务，100ms周期
    {oled_task, 10,   0},    // OLED任务，10ms周期
    {uart_task, 5,    0},    // 串口任务，5ms周期
    {rtc_task,  500,  0}     // RTC任务，500ms周期
};
```

### 执行特点
- RTC任务每500ms执行一次
- 内部通过秒数变化检测避免重复输出
- 实际输出频率为每秒一次

## 故障排除

### 无时间显示
1. 检查RTC是否正确初始化
2. 确认调度器正常运行
3. 检查串口输出功能

### 时间显示错误
1. 检查RTC时钟源配置
2. 确认RTC初始时间设置
3. 检查BCD格式转换

### 命令无响应
1. 确认串口接收功能正常
2. 检查命令格式是否正确
3. 验证串口任务是否运行

## 扩展建议

### 可添加的功能
1. **时间设置命令** - 通过串口设置RTC时间
2. **时间格式选择** - 12/24小时制切换
3. **定时提醒** - 设置闹钟功能
4. **时间戳记录** - 记录特定事件的时间

### 优化建议
1. **减少输出频率** - 可配置的显示间隔
2. **时区支持** - 添加时区转换功能
3. **日期计算** - 添加日期运算功能

现在您可以通过串口观察RTC时间的实时显示，或者发送"time"命令查看详细的时间信息！
