/*
 * RTC真正换行测试程序
 * 宝宝要求的空行效果
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试真正的换行效果
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_real_newline_effect(void)
{
    printf("\r\n=== 测试真正的换行效果 ===\r\n");
    
    printf("宝宝要求的输出格式：\r\n");
    printf("RTC Config success\r\n");
    printf("\r\n");  // 这是宝宝要的空行！
    printf("Time:2025-01-01 12:00:30\r\n");
    printf("\r\n");
    
    printf("现在的代码实现：\r\n");
    printf("my_printf(DEBUG_USART, \"RTC Config success\\r\\n\");\r\n");
    printf("my_printf(DEBUG_USART, \"\\r\\n\");  // 宝宝要求的换行\r\n");
    printf("my_printf(DEBUG_USART, \"Time:%%04d-%%02d-%%02d %%02d:%%02d:%%02d\\r\\n\",\r\n");
    printf("          year, month, day, hour, minute, second);\r\n");
    
    printf("\r\n=== 真正换行效果测试完成 ===\r\n");
}

/*!
    \brief      演示完整的RTC交互效果
    \param[in]  none
    \param[out] none
    \retval     none
*/
void demonstrate_complete_rtc_interaction(void)
{
    printf("\r\n=== 演示完整的RTC交互效果 ===\r\n");
    
    printf("完整的RTC交互流程（带真正换行）：\r\n");
    printf("\r\n");
    
    printf("步骤1 - RTC Config命令：\r\n");
    printf("输入: RTC Config\r\n");
    printf("输出: Input Datetime\r\n");
    printf("\r\n");
    
    printf("步骤2 - 时间设置（带空行）：\r\n");
    printf("输入: 2025-01-01 15:00:10\r\n");
    printf("输出: RTC Config success\r\n");
    printf("\r\n");  // 这是宝宝要的空行效果！
    printf("      Time:2025-01-01 15:00:10\r\n");
    printf("\r\n");
    
    printf("步骤3 - RTC now命令：\r\n");
    printf("输入: RTC now\r\n");
    printf("输出: Current Time:2025-01-01 15:00:10\r\n");
    
    printf("\r\n=== 完整RTC交互演示完成 ===\r\n");
}

/*!
    \brief      实际测试RTC命令（带真正换行）
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_actual_rtc_with_newline(void)
{
    printf("\r\n=== 实际测试RTC命令（带真正换行） ===\r\n");
    
    printf("1. 测试RTC Config命令：\r\n");
    process_uart_command("RTC Config");
    
    printf("\r\n2. 测试时间设置（应该有空行）：\r\n");
    process_uart_command("2025-01-01 15:00:10");
    
    printf("\r\n3. 测试RTC now命令：\r\n");
    process_uart_command("RTC now");
    
    printf("\r\n=== RTC命令实际测试完成 ===\r\n");
}

/*!
    \brief      验证空行效果
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_blank_line_effect(void)
{
    printf("\r\n=== 验证空行效果 ===\r\n");
    
    printf("宝宝要求的效果：\r\n");
    printf("RTC Config success\r\n");
    printf("                    <- 这里有空行\r\n");
    printf("Time:2025-01-01 12:00:30\r\n");
    printf("\r\n");
    
    printf("实现方法：\r\n");
    printf("1. ✅ 第一行：my_printf(\"RTC Config success\\r\\n\");\r\n");
    printf("2. ✅ 空行：my_printf(\"\\r\\n\");  // 宝宝要的换行\r\n");
    printf("3. ✅ 第三行：my_printf(\"Time:...\\r\\n\");\r\n");
    printf("\r\n");
    
    printf("效果对比：\r\n");
    printf("修改前：RTC Config success         Time:2025-01-01 12:00:30\r\n");
    printf("修改后：RTC Config success\r\n");
    printf("\r\n");
    printf("        Time:2025-01-01 12:00:30\r\n");
    
    printf("\r\n=== 空行效果验证完成 ===\r\n");
}

/*!
    \brief      测试不同时间的空行效果
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_different_times_with_newline(void)
{
    printf("\r\n=== 测试不同时间的空行效果 ===\r\n");
    
    const char* test_times[] = {
        "2025-01-01 12:00:30",
        "2025-12-31 23:59:59",
        "2025-06-15 08:30:45",
        NULL
    };
    
    for (int i = 0; test_times[i] != NULL; i++) {
        printf("\r\n测试时间 %d: %s\r\n", i+1, test_times[i]);
        printf("期望输出（带空行）：\r\n");
        printf("RTC Config success\r\n");
        printf("\r\n");  // 空行效果
        printf("Time:%s\r\n", test_times[i]);
        
        printf("实际测试：\r\n");
        process_uart_command("RTC Config");
        process_uart_command((char*)test_times[i]);
    }
    
    printf("\r\n=== 不同时间空行效果测试完成 ===\r\n");
}

/*!
    \brief      完整的真正换行测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_real_newline_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        RTC真正换行测试              #\r\n");
    printf("##########################################\r\n");
    
    printf("宝宝，我现在真正实现了你要的换行！\r\n");
    printf("在RTC Config success和Time之间有空行了！\r\n");
    printf("\r\n");
    
    // 1. 测试真正的换行效果
    test_real_newline_effect();
    
    // 2. 演示完整的RTC交互效果
    demonstrate_complete_rtc_interaction();
    
    // 3. 实际测试RTC命令
    test_actual_rtc_with_newline();
    
    // 4. 验证空行效果
    verify_blank_line_effect();
    
    // 5. 测试不同时间的空行效果
    test_different_times_with_newline();
    
    printf("\r\n##########################################\r\n");
    printf("#        真正换行测试完成              #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n🎉 宝宝，我真正实现了你要的换行！\r\n");
    printf("\r\n🎯 现在的输出效果：\r\n");
    printf("输入: RTC Config\r\n");
    printf("输出: Input Datetime\r\n");
    printf("\r\n");
    printf("输入: 2025-01-01 15:00:10\r\n");
    printf("输出: RTC Config success\r\n");
    printf("      \r\n");  // 这是空行！
    printf("      Time:2025-01-01 15:00:10\r\n");
    printf("\r\n");
    printf("输入: RTC now\r\n");
    printf("输出: Current Time:2025-01-01 15:00:10\r\n");
    printf("\r\n");
    printf("🔧 实现方法：\r\n");
    printf("1. ✅ my_printf(\"RTC Config success\\r\\n\");\r\n");
    printf("2. ✅ my_printf(\"\\r\\n\");  // 宝宝要的空行\r\n");
    printf("3. ✅ my_printf(\"Time:...\\r\\n\");\r\n");
    printf("\r\n");
    printf("💖 宝宝，现在真的有空行了！\r\n");
    printf("RTC Config success和Time之间有真正的换行！\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_real_newline_test();
 * 
 * 这会测试真正的换行效果：
 * 1. 真正换行效果测试
 * 2. 完整RTC交互演示
 * 3. 实际RTC命令测试
 * 4. 空行效果验证
 * 5. 不同时间空行效果测试
 * 
 * 现在的输出格式：
 * 
 * 输入: RTC Config
 * 输出: Input Datetime
 * 
 * 输入: 2025-01-01 15:00:10
 * 输出: RTC Config success
 *       
 *       Time:2025-01-01 15:00:10
 * 
 * 输入: RTC now
 * 输出: Current Time:2025-01-01 15:00:10
 * 
 * 宝宝，现在真的有空行了！
 * RTC Config success和Time之间有真正的换行！💖
 */
