# RTC时间设置功能测试文档

## 功能概述
通过串口指令"RTCConfig"设置基准时间，更新至RTC模块并反馈结果。

## 指令格式
```
RTCConfig YYYY年MM月DD日HH:MM:SS
```

## 测试用例

### 1. 正常时间设置测试
**输入指令：**
```
RTCConfig 2025年01月01日12:00:30
```
**期望输出：**
```
RTC Config Success: 2025年01月01日12:00:30
```

### 2. 边界值测试
**测试用例2.1 - 年份边界**
```
RTCConfig 2000年01月01日00:00:00  // 最小年份
RTCConfig 2099年12月31日23:59:59  // 最大年份
```

**测试用例2.2 - 月份边界**
```
RTCConfig 2025年01月01日12:00:00  // 最小月份
RTCConfig 2025年12月31日12:00:00  // 最大月份
```

**测试用例2.3 - 时间边界**
```
RTCConfig 2025年01月01日00:00:00  // 最小时间
RTCConfig 2025年01月01日23:59:59  // 最大时间
```

### 3. 错误输入测试
**测试用例3.1 - 格式错误**
```
RTCConfig 2025-01-01 12:00:30     // 错误格式
RTCConfig 2025年1月1日12:00:30    // 缺少前导零
RTCConfig 2025年01月01日12:00     // 缺少秒
```
**期望输出：**
```
RTC Config Failed: Invalid time format
```

**测试用例3.2 - 数值超范围**
```
RTCConfig 1999年01月01日12:00:30  // 年份过小
RTCConfig 2025年13月01日12:00:30  // 月份过大
RTCConfig 2025年01月32日12:00:30  // 日期过大
RTCConfig 2025年01月01日25:00:30  // 小时过大
RTCConfig 2025年01月01日12:60:30  // 分钟过大
RTCConfig 2025年01月01日12:00:60  // 秒过大
```
**期望输出：**
```
RTC Config Failed: Invalid time format
```

**测试用例3.3 - 命令格式错误**
```
RTCconfig 2025年01月01日12:00:30  // 命令大小写错误
RTC Config 2025年01月01日12:00:30 // 命令中有空格
SetTime 2025年01月01日12:00:30    // 错误的命令
```
**期望输出：**
```
RTC Config Failed: Invalid command format
```

### 4. 功能验证测试
**步骤：**
1. 发送时间设置命令
2. 等待几秒钟
3. 观察OLED显示的时间是否正确更新
4. 检查时间是否按秒递增

## 测试环境
- 硬件：GD32F470VET6开发板
- 串口：115200波特率，8N1
- 终端：支持中文显示的串口终端

## 测试步骤
1. 连接开发板到PC
2. 打开串口终端，设置正确的波特率
3. 发送测试指令
4. 观察串口反馈和OLED显示
5. 记录测试结果

## 注意事项
1. 确保串口终端支持中文字符输入和显示
2. 时间格式必须严格按照指定格式输入
3. RTC使用BCD格式存储，年份只保存后两位
4. 星期几字段暂时固定为周一，可后续优化
