/*
 * SD卡配置文件读取调试助手
 * 帮助宝宝排查为什么读不出config.ini文件
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      调试SD卡挂载状态
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_sd_card_mount(void)
{
    printf("\r\n=== 调试SD卡挂载状态 ===\r\n");
    
    extern FATFS fs;
    FRESULT result = f_mount(0, &fs);
    
    printf("f_mount结果: ");
    switch(result) {
        case FR_OK:
            printf("FR_OK - 挂载成功\r\n");
            break;
        case FR_INVALID_DRIVE:
            printf("FR_INVALID_DRIVE - 无效驱动器\r\n");
            break;
        case FR_NOT_READY:
            printf("FR_NOT_READY - 设备未就绪\r\n");
            break;
        case FR_NO_FILESYSTEM:
            printf("FR_NO_FILESYSTEM - 无文件系统\r\n");
            break;
        default:
            printf("错误代码: %d\r\n", result);
            break;
    }
    
    printf("=== SD卡挂载调试完成 ===\r\n");
}

/*!
    \brief      调试文件系统根目录
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_root_directory(void)
{
    printf("\r\n=== 调试根目录文件列表 ===\r\n");
    
    DIR dir;
    FILINFO fno;
    FRESULT result;
    
    // 打开根目录
    result = f_opendir(&dir, "0:/");
    if (result == FR_OK) {
        printf("根目录打开成功，文件列表:\r\n");
        
        // 读取目录内容
        while (1) {
            result = f_readdir(&dir, &fno);
            if (result != FR_OK || fno.fname[0] == 0) break;
            
            printf("  %s", fno.fname);
            if (fno.fattrib & AM_DIR) {
                printf(" [目录]\r\n");
            } else {
                printf(" [文件, %d字节]\r\n", fno.fsize);
            }
        }
        // FATFS不需要显式关闭目录
    } else {
        printf("无法打开根目录，错误: %d\r\n", result);
    }
    
    printf("=== 根目录调试完成 ===\r\n");
}

/*!
    \brief      调试config.ini文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_config_ini_file(void)
{
    printf("\r\n=== 调试config.ini文件 ===\r\n");
    
    FIL file;
    FRESULT result;
    
    // 尝试不同的文件路径
    const char* paths[] = {
        "0:/config.ini",
        "config.ini",
        "0:config.ini",
        "/config.ini",
        NULL
    };
    
    for (int i = 0; paths[i] != NULL; i++) {
        printf("尝试路径: %s\r\n", paths[i]);
        result = f_open(&file, paths[i], FA_READ);
        
        if (result == FR_OK) {
            printf("  ✅ 文件打开成功！\r\n");
            
            // 读取文件大小
            DWORD file_size = f_size(&file);
            printf("  文件大小: %d 字节\r\n", file_size);
            
            // 读取前100个字符
            char buffer[101];
            UINT bytes_read;
            result = f_read(&file, buffer, 100, &bytes_read);
            if (result == FR_OK) {
                buffer[bytes_read] = '\0';
                printf("  文件内容预览:\r\n");
                printf("  %s\r\n", buffer);
            }
            
            f_close(&file);
            break;
        } else {
            printf("  ❌ 文件打开失败，错误: %d\r\n", result);
        }
    }
    
    printf("=== config.ini文件调试完成 ===\r\n");
}

/*!
    \brief      测试文件创建
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_file_creation(void)
{
    printf("\r\n=== 测试文件创建 ===\r\n");
    
    FIL file;
    FRESULT result;
    
    // 尝试创建测试文件
    result = f_open(&file, "0:/test.txt", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        printf("✅ 测试文件创建成功\r\n");
        
        // 写入测试内容
        const char* test_content = "Test file created successfully!";
        UINT bytes_written;
        result = f_write(&file, test_content, strlen(test_content), &bytes_written);
        
        if (result == FR_OK) {
            printf("✅ 测试内容写入成功，写入%d字节\r\n", bytes_written);
        } else {
            printf("❌ 测试内容写入失败，错误: %d\r\n", result);
        }
        
        f_close(&file);
        
        // 尝试删除测试文件
        f_unlink("0:/test.txt");
        printf("测试文件已删除\r\n");
    } else {
        printf("❌ 测试文件创建失败，错误: %d\r\n", result);
        printf("可能原因: SD卡写保护或文件系统只读\r\n");
    }
    
    printf("=== 文件创建测试完成 ===\r\n");
}

/*!
    \brief      完整的SD卡调试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_sd_card_debug(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        SD卡配置文件调试助手          #\r\n");
    printf("##########################################\r\n");
    
    // 1. 调试SD卡挂载
    debug_sd_card_mount();
    
    // 2. 调试根目录
    debug_root_directory();
    
    // 3. 调试config.ini文件
    debug_config_ini_file();
    
    // 4. 测试文件创建
    test_file_creation();
    
    printf("\r\n##########################################\r\n");
    printf("#        调试完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n🔍 可能的问题和解决方案:\r\n");
    printf("1. 文件路径问题 - 检查文件是否在根目录\r\n");
    printf("2. 文件名大小写 - 确保文件名完全匹配\r\n");
    printf("3. SD卡格式 - 确保是FAT32格式\r\n");
    printf("4. 文件编码 - 确保是标准ASCII编码\r\n");
    printf("5. SD卡连接 - 检查硬件连接\r\n");
    
    printf("\r\n📋 建议的config.ini文件格式:\r\n");
    printf("[Ratio]\r\n");
    printf("Ch0 = 1.99\r\n");
    printf("\r\n");
    printf("[Limit]\r\n");
    printf("Ch0 = 10.11\r\n");
    
    printf("\r\n💡 调试步骤:\r\n");
    printf("1. 运行这个调试程序\r\n");
    printf("2. 检查SD卡挂载状态\r\n");
    printf("3. 查看根目录文件列表\r\n");
    printf("4. 确认config.ini文件存在\r\n");
    printf("5. 检查文件内容格式\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_sd_card_debug();
 * 
 * 这会帮助你调试：
 * 1. SD卡挂载状态
 * 2. 根目录文件列表
 * 3. config.ini文件访问
 * 4. 文件系统读写能力
 * 
 * 根据调试结果，你可以：
 * 1. 确认SD卡是否正确挂载
 * 2. 确认config.ini文件是否存在
 * 3. 确认文件路径是否正确
 * 4. 确认文件内容格式是否正确
 * 
 * 宝宝，运行这个调试程序，告诉我结果，我帮你解决问题！
 */
