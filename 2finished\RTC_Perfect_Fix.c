/*
 * RTC完美修复方案 - 专门解决RTC Config失败问题
 * 宝宝，这个文件包含了完整的RTC修复代码！
 */

#include "mcu_cmic_gd32f470vet6.h"

// 外部变量声明
extern rtc_parameter_struct rtc_initpara;
extern uint32_t prescaler_a, prescaler_s;

/*!
    \brief      十进制转BCD格式 - 修复版本
    \param[in]  decimal: 十进制数值
    \param[out] none
    \retval     uint8_t: BCD格式数值
*/
uint8_t decimal_to_bcd_fixed(uint8_t decimal)
{
    return ((decimal / 10) << 4) | (decimal % 10);
}

/*!
    \brief      BCD转十进制 - 修复版本
    \param[in]  bcd: BCD格式数值
    \param[out] none
    \retval     uint8_t: 十进制数值
*/
uint8_t bcd_to_decimal_fixed(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

/*!
    \brief      月份转换为RTC枚举值 - 修复版本
    \param[in]  month: 月份数字 (1-12)
    \param[out] none
    \retval     uint32_t: RTC月份枚举值
*/
uint32_t month_to_rtc_enum_fixed(int month)
{
    switch(month) {
        case 1: return RTC_JAN;
        case 2: return RTC_FEB;
        case 3: return RTC_MAR;
        case 4: return RTC_APR;
        case 5: return RTC_MAY;
        case 6: return RTC_JUN;
        case 7: return RTC_JUL;
        case 8: return RTC_AUG;
        case 9: return RTC_SEP;
        case 10: return RTC_OCT;
        case 11: return RTC_NOV;
        case 12: return RTC_DEC;
        default: return RTC_JAN;
    }
}

/*!
    \brief      RTC枚举值转换为月份数字 - 修复版本
    \param[in]  rtc_month: RTC月份枚举值
    \param[out] none
    \retval     int: 月份数字 (1-12)
*/
int rtc_enum_to_month_fixed(uint32_t rtc_month)
{
    switch(rtc_month) {
        case RTC_JAN: return 1;
        case RTC_FEB: return 2;
        case RTC_MAR: return 3;
        case RTC_APR: return 4;
        case RTC_MAY: return 5;
        case RTC_JUN: return 6;
        case RTC_JUL: return 7;
        case RTC_AUG: return 8;
        case RTC_SEP: return 9;
        case RTC_OCT: return 10;
        case RTC_NOV: return 11;
        case RTC_DEC: return 12;
        default: return 1;
    }
}

/*!
    \brief      RTC基础初始化 - 完美修复版本
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_basic_init_fixed(void)
{
    // 1. 使能PMU时钟
    rcu_periph_clock_enable(RCU_PMU);
    
    // 2. 使能备份域写访问权限 (关键步骤!)
    pmu_backup_write_enable();
    
    // 3. 配置RTC时钟源 - 使用LXTAL (32.768kHz)
    rcu_osci_on(RCU_LXTAL);
    rcu_osci_stab_wait(RCU_LXTAL);
    rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);
    
    // 4. 使能RTC时钟
    rcu_periph_clock_enable(RCU_RTC);
    
    // 5. 等待RTC寄存器同步
    rtc_register_sync_wait();
    
    // 6. 设置正确的预分频器值
    prescaler_a = 0x7F;  // 异步预分频器 (128-1) for LXTAL
    prescaler_s = 0xFF;  // 同步预分频器 (256-1) for LXTAL
    // 最终频率 = 32768 / (128 * 256) = 1Hz
}

/*!
    \brief      RTC时间设置 - 完美修复版本
    \param[in]  time_str: 时间字符串 "2025-01-01 15:00:10"
    \param[out] none
    \retval     int: 0=成功, -1=失败
*/
int rtc_set_time_fixed(const char* time_str)
{
    int year, month, day, hour, minute, second;
    rtc_parameter_struct rtc_time;
    
    // 解析时间字符串
    int parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", 
                       &year, &month, &day, &hour, &minute, &second);
    
    if (parsed != 6) {
        return -1; // 解析失败
    }
    
    // 验证时间范围
    if (year < 2000 || year > 2099 ||
        month < 1 || month > 12 ||
        day < 1 || day > 31 ||
        hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 ||
        second < 0 || second > 59) {
        return -1; // 范围无效
    }
    
    // 确保RTC基础配置已完成
    rtc_basic_init_fixed();
    
    // 添加延时确保RTC稳定
    delay_ms(100);
    
    // 设置RTC参数结构体
    rtc_time.year = decimal_to_bcd_fixed(year - 2000);      // 年份：只存储后两位
    rtc_time.month = month_to_rtc_enum_fixed(month);        // 月份：转换为枚举值
    rtc_time.date = decimal_to_bcd_fixed(day);              // 日期：BCD格式
    rtc_time.hour = decimal_to_bcd_fixed(hour);             // 小时：BCD格式
    rtc_time.minute = decimal_to_bcd_fixed(minute);         // 分钟：BCD格式
    rtc_time.second = decimal_to_bcd_fixed(second);         // 秒：BCD格式
    rtc_time.day_of_week = RTC_MONDAY;                      // 星期：默认周一
    rtc_time.display_format = RTC_24HOUR;                   // 显示格式：24小时制
    rtc_time.am_pm = RTC_AM;                                // AM/PM：上午
    
    // 设置预分频器 - 使用LXTAL的正确配置
    rtc_time.factor_asyn = 0x7F;   // 异步预分频器 (128-1)
    rtc_time.factor_syn = 0xFF;    // 同步预分频器 (256-1)
    
    // 调用RTC初始化函数
    if (rtc_init(&rtc_time) == SUCCESS) {
        return 0;  // 成功
    } else {
        return -1; // 失败
    }
}

/*!
    \brief      获取RTC时间 - 完美修复版本
    \param[out] year: 年份指针
    \param[out] month: 月份指针
    \param[out] day: 日期指针
    \param[out] hour: 小时指针
    \param[out] minute: 分钟指针
    \param[out] second: 秒指针
    \param[out] none
    \retval     none
*/
void rtc_get_time_fixed(int *year, int *month, int *day, int *hour, int *minute, int *second)
{
    rtc_parameter_struct rtc_time;
    
    // 读取当前RTC时间
    rtc_current_time_get(&rtc_time);
    
    // 转换并返回时间值
    *year = 2000 + bcd_to_decimal_fixed(rtc_time.year);     // 年份：加上2000
    *month = rtc_enum_to_month_fixed(rtc_time.month);       // 月份：枚举值转数字
    *day = bcd_to_decimal_fixed(rtc_time.date);             // 日期：BCD转十进制
    *hour = bcd_to_decimal_fixed(rtc_time.hour);            // 小时：BCD转十进制
    *minute = bcd_to_decimal_fixed(rtc_time.minute);        // 分钟：BCD转十进制
    *second = bcd_to_decimal_fixed(rtc_time.second);        // 秒：BCD转十进制
}

/*!
    \brief      处理RTC Config命令 - 完美修复版本
    \param[in]  time_str: 时间字符串
    \param[out] none
    \retval     none
*/
void process_rtc_config_fixed(const char* time_str)
{
    // 调用修复版本的RTC设置函数
    if (rtc_set_time_fixed(time_str) == 0) {
        // 成功：解析时间并输出
        int year, month, day, hour, minute, second;
        sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
        
        // 记录成功日志
        write_log_entry("rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
                       year, month, day, hour, minute, second);
        
        // 按照题目要求的格式输出
        my_printf(DEBUG_USART, "RTC Config success         Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                  year, month, day, hour, minute, second);
    } else {
        // 失败
        my_printf(DEBUG_USART, "RTC Config failed\r\n");
    }
}

/*!
    \brief      处理RTC now命令 - 完美修复版本
    \param[in]  none
    \param[out] none
    \retval     none
*/
void process_rtc_now_fixed(void)
{
    int year, month, day, hour, minute, second;
    
    // 获取当前时间
    rtc_get_time_fixed(&year, &month, &day, &hour, &minute, &second);
    
    // 按照题目要求的格式输出
    my_printf(DEBUG_USART, "Current Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
              year, month, day, hour, minute, second);
}

/*
 * 使用说明：
 * 
 * 1. 在usart_app.c中替换原来的RTC处理函数：
 *    - 将process_rtc_time_input()替换为process_rtc_config_fixed()
 *    - 将process_rtc_now_command()替换为process_rtc_now_fixed()
 * 
 * 2. 确保在main.c中调用了bsp_rtc_init()
 * 
 * 3. 测试步骤：
 *    - 输入"RTC Config" → 应该显示"Input Datetime"
 *    - 输入"2025-01-01 15:00:10" → 应该显示"RTC Config success         Time:2025-01-01 15:00:10"
 *    - 输入"RTC now" → 应该显示"Current Time:2025-01-01 15:00:xx"
 */
