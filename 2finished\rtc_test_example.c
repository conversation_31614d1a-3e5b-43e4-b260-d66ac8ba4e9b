/*
 * RTC功能完整测试示例
 * 这个文件展示了如何正确实现RTC功能
 * 
 * 主要问题和解决方案：
 * 1. 月份枚举值转换问题 - 已修复
 * 2. BCD格式转换问题 - 已修复  
 * 3. RTC时钟源配置问题 - 需要检查
 * 4. 预分频器设置问题 - 已修复
 */

#include "mcu_cmic_gd32f470vet6.h"

// 外部变量声明
extern rtc_parameter_struct rtc_initpara;

/*!
    \brief      十进制转BCD格式
    \param[in]  decimal: 十进制数值 (0-99)
    \param[out] none
    \retval     uint8_t: BCD格式数值
*/
uint8_t decimal_to_bcd(uint8_t decimal)
{
    return ((decimal / 10) << 4) | (decimal % 10);
}

/*!
    \brief      BCD格式转十进制
    \param[in]  bcd: BCD格式数值
    \param[out] none
    \retval     uint8_t: 十进制数值
*/
uint8_t bcd_to_decimal(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

/*!
    \brief      月份数字转RTC枚举值
    \param[in]  month: 月份 (1-12)
    \param[out] none
    \retval     uint32_t: RTC月份枚举值
*/
uint32_t month_to_rtc_enum(int month)
{
    switch(month) {
        case 1: return RTC_JAN;
        case 2: return RTC_FEB;
        case 3: return RTC_MAR;
        case 4: return RTC_APR;
        case 5: return RTC_MAY;
        case 6: return RTC_JUN;
        case 7: return RTC_JUL;
        case 8: return RTC_AUG;
        case 9: return RTC_SEP;
        case 10: return RTC_OCT;
        case 11: return RTC_NOV;
        case 12: return RTC_DEC;
        default: return RTC_JAN;
    }
}

/*!
    \brief      RTC枚举值转月份数字
    \param[in]  rtc_month: RTC月份枚举值
    \param[out] none
    \retval     int: 月份数字 (1-12)
*/
int rtc_enum_to_month(uint32_t rtc_month)
{
    switch(rtc_month) {
        case RTC_JAN: return 1;
        case RTC_FEB: return 2;
        case RTC_MAR: return 3;
        case RTC_APR: return 4;
        case RTC_MAY: return 5;
        case RTC_JUN: return 6;
        case RTC_JUL: return 7;
        case RTC_AUG: return 8;
        case RTC_SEP: return 9;
        case RTC_OCT: return 10;
        case RTC_NOV: return 11;
        case RTC_DEC: return 12;
        default: return 1;
    }
}

/*!
    \brief      设置RTC时间 - 完整版本
    \param[in]  year: 年份 (2000-2099)
    \param[in]  month: 月份 (1-12)
    \param[in]  day: 日期 (1-31)
    \param[in]  hour: 小时 (0-23)
    \param[in]  minute: 分钟 (0-59)
    \param[in]  second: 秒 (0-59)
    \param[out] none
    \retval     int: 0=成功, -1=失败
*/
int rtc_set_time_complete(int year, int month, int day, int hour, int minute, int second)
{
    rtc_parameter_struct rtc_time;
    
    // 参数验证
    if (year < 2000 || year > 2099 ||
        month < 1 || month > 12 ||
        day < 1 || day > 31 ||
        hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 ||
        second < 0 || second > 59) {
        return -1;  // 参数错误
    }
    
    // 设置RTC参数结构体
    rtc_time.year = decimal_to_bcd(year - 2000);      // 年份：只存储后两位
    rtc_time.month = month_to_rtc_enum(month);        // 月份：转换为枚举值
    rtc_time.date = decimal_to_bcd(day);              // 日期：BCD格式
    rtc_time.hour = decimal_to_bcd(hour);             // 小时：BCD格式
    rtc_time.minute = decimal_to_bcd(minute);         // 分钟：BCD格式
    rtc_time.second = decimal_to_bcd(second);         // 秒：BCD格式
    rtc_time.day_of_week = RTC_MONDAY;                // 星期：默认周一
    rtc_time.display_format = RTC_24HOUR;             // 显示格式：24小时制
    rtc_time.am_pm = RTC_AM;                          // AM/PM：上午
    
    // 设置预分频器 - 这很重要！
    // 对于LXTAL (32.768kHz)时钟源：
    rtc_time.factor_asyn = 0x7F;   // 异步预分频器 (128-1)
    rtc_time.factor_syn = 0xFF;    // 同步预分频器 (256-1)
    // 最终频率 = 32768 / (128 * 256) = 1Hz
    
    // 调用RTC初始化函数
    if (rtc_init(&rtc_time) == SUCCESS) {
        return 0;  // 成功
    } else {
        return -1; // 失败
    }
}

/*!
    \brief      获取RTC时间 - 完整版本
    \param[out] year: 年份指针
    \param[out] month: 月份指针
    \param[out] day: 日期指针
    \param[out] hour: 小时指针
    \param[out] minute: 分钟指针
    \param[out] second: 秒指针
    \param[out] none
    \retval     none
*/
void rtc_get_time_complete(int *year, int *month, int *day, int *hour, int *minute, int *second)
{
    rtc_parameter_struct rtc_time;
    
    // 读取当前RTC时间
    rtc_current_time_get(&rtc_time);
    
    // 转换并返回时间值
    *year = 2000 + bcd_to_decimal(rtc_time.year);     // 年份：加上2000
    *month = rtc_enum_to_month(rtc_time.month);       // 月份：枚举值转数字
    *day = bcd_to_decimal(rtc_time.date);             // 日期：BCD转十进制
    *hour = bcd_to_decimal(rtc_time.hour);            // 小时：BCD转十进制
    *minute = bcd_to_decimal(rtc_time.minute);        // 分钟：BCD转十进制
    *second = bcd_to_decimal(rtc_time.second);        // 秒：BCD转十进制
}

/*!
    \brief      RTC功能测试函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_test_example(void)
{
    int year, month, day, hour, minute, second;
    
    // 测试1：设置时间为 2025-06-15 14:30:25
    printf("=== RTC功能测试 ===\r\n");
    printf("设置时间: 2025-06-15 14:30:25\r\n");
    
    if (rtc_set_time_complete(2025, 6, 15, 14, 30, 25) == 0) {
        printf("RTC时间设置成功！\r\n");
    } else {
        printf("RTC时间设置失败！\r\n");
        return;
    }
    
    // 等待一段时间
    for(volatile int i = 0; i < 1000000; i++);
    
    // 测试2：读取时间
    rtc_get_time_complete(&year, &month, &day, &hour, &minute, &second);
    printf("读取时间: %04d-%02d-%02d %02d:%02d:%02d\r\n", 
           year, month, day, hour, minute, second);
    
    printf("=== 测试完成 ===\r\n");
}

/*!
    \brief      处理串口RTC配置命令 - 改进版本
    \param[in]  time_str: 时间字符串
    \param[out] none
    \retval     none
*/
void process_rtc_config_improved(char* time_str)
{
    int year, month, day, hour, minute, second;
    
    // 解析时间字符串
    int parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", 
                       &year, &month, &day, &hour, &minute, &second);
    
    if (parsed == 6) {
        // 设置RTC时间
        if (rtc_set_time_complete(year, month, day, hour, minute, second) == 0) {
            printf("RTC Config success Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                   year, month, day, hour, minute, second);
        } else {
            printf("RTC Config failed\r\n");
        }
    } else {
        printf("Invalid time format. Use: YYYY-MM-DD HH:MM:SS\r\n");
    }
}

/*!
    \brief      显示当前RTC时间 - 改进版本
    \param[in]  none
    \param[out] none
    \retval     none
*/
void show_rtc_time_improved(void)
{
    int year, month, day, hour, minute, second;
    
    // 获取当前时间
    rtc_get_time_complete(&year, &month, &day, &hour, &minute, &second);
    
    // 按要求格式输出
    printf("Current Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
           year, month, day, hour, minute, second);
}

/*
 * 使用说明：
 * 
 * 1. 在main函数中调用 bsp_rtc_init() 初始化RTC
 * 2. 使用 rtc_set_time_complete() 设置时间
 * 3. 使用 rtc_get_time_complete() 获取时间
 * 4. 在串口命令处理中使用 process_rtc_config_improved()
 * 5. 使用 show_rtc_time_improved() 显示当前时间
 * 
 * 关键修复点：
 * - 正确的月份枚举值转换
 * - 正确的BCD格式转换
 * - 正确的预分频器设置
 * - 完整的参数验证
 * - 标准的时间格式输出
 */
