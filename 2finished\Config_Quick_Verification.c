/*
 * config.ini功能快速验证程序
 * 用于快速测试和验证配置文件读取功能
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      快速验证配置文件功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void quick_verify_config_function(void)
{
    printf("\r\n=== config.ini功能快速验证 ===\r\n");
    
    printf("题目要求验证：\r\n");
    printf("1. 输入 'conf' 命令\r\n");
    printf("2. 文件不存在时输出: config.ini file not found.\r\n");
    printf("3. 文件存在时输出: Ratio = xxxx, Limit = xxxx, config read success\r\n");
    printf("\r\n");
    
    // 测试1：文件不存在的情况
    printf("测试1：删除配置文件，测试文件不存在情况\r\n");
    f_unlink("0:/config.ini");  // 删除文件
    printf("发送命令: conf\r\n");
    process_uart_command("conf");
    printf("\r\n");
    
    // 测试2：创建标准配置文件并测试
    printf("测试2：创建标准配置文件，测试文件存在情况\r\n");
    
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    const char* standard_config = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, standard_config, strlen(standard_config), &bytes_written);
        f_close(&config_file);
        printf("标准配置文件创建成功\r\n");
        printf("文件内容:\r\n%s\r\n", standard_config);
        
        printf("发送命令: conf\r\n");
        process_uart_command("conf");
        
        // 验证读取的值
        printf("\r\n验证读取的配置值:\r\n");
        printf("config_ratio = %.2f\r\n", get_config_ratio());
        printf("config_limit = %.2f\r\n", get_config_limit());
    } else {
        printf("创建配置文件失败，错误码: %d\r\n", result);
    }
    
    printf("\r\n=== 验证完成 ===\r\n");
    printf("现在可以手动通过串口发送 'conf' 命令进行测试\r\n");
}

/*!
    \brief      创建多种测试配置文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void create_various_test_configs(void)
{
    printf("\r\n=== 创建多种测试配置文件 ===\r\n");
    
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    // 测试配置1：不同的数值
    const char* test_config1 = 
        "[Ratio]\r\n"
        "Ch0 = 2.50\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 15.75\r\n";
    
    printf("1. 创建测试配置1 (Ratio=2.50, Limit=15.75)\r\n");
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, test_config1, strlen(test_config1), &bytes_written);
        f_close(&config_file);
        printf("发送命令: conf\r\n");
        process_uart_command("conf");
        printf("\r\n");
    }
    
    // 测试配置2：带空格的格式
    const char* test_config2 = 
        "[Ratio]\r\n"
        "Ch0  =  3.14  \r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0  =  20.00  \r\n";
    
    printf("2. 创建测试配置2 (带空格格式)\r\n");
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, test_config2, strlen(test_config2), &bytes_written);
        f_close(&config_file);
        printf("发送命令: conf\r\n");
        process_uart_command("conf");
        printf("\r\n");
    }
    
    // 测试配置3：带注释的格式
    const char* test_config3 = 
        "; 这是配置文件注释\r\n"
        "[Ratio]\r\n"
        "Ch0 = 1.23\r\n"
        "\r\n"
        "; 阈值配置\r\n"
        "[Limit]\r\n"
        "Ch0 = 5.67\r\n";
    
    printf("3. 创建测试配置3 (带注释格式)\r\n");
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, test_config3, strlen(test_config3), &bytes_written);
        f_close(&config_file);
        printf("发送命令: conf\r\n");
        process_uart_command("conf");
        printf("\r\n");
    }
    
    printf("=== 多种配置测试完成 ===\r\n");
}

/*!
    \brief      验证输出格式是否符合题目要求
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_output_format(void)
{
    printf("\r\n=== 验证输出格式 ===\r\n");
    
    printf("题目要求的输出格式：\r\n");
    printf("文件不存在时:\r\n");
    printf("  输入: conf\r\n");
    printf("  输出: config.ini file not found.\r\n");
    printf("\r\n");
    printf("文件存在时:\r\n");
    printf("  输入: conf\r\n");
    printf("  输出: Ratio = xxxx\r\n");
    printf("        Limit = xxxx\r\n");
    printf("        config read success\r\n");
    printf("\r\n");
    
    printf("实际测试输出：\r\n");
    
    // 创建标准配置文件
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        printf("文件存在时的输出：\r\n");
        process_uart_command("conf");
        printf("\r\n");
        
        // 删除文件测试
        f_unlink("0:/config.ini");
        printf("文件不存在时的输出：\r\n");
        process_uart_command("conf");
    }
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*!
    \brief      完整的功能验证程序
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_config_verification(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        config.ini功能完整验证        #\r\n");
    printf("##########################################\r\n");
    
    // 1. 快速功能验证
    quick_verify_config_function();
    
    // 2. 多种配置测试
    create_various_test_configs();
    
    // 3. 输出格式验证
    verify_output_format();
    
    printf("\r\n##########################################\r\n");
    printf("#        验证完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n手动测试说明：\r\n");
    printf("1. 通过串口发送 'conf' 命令\r\n");
    printf("2. 观察输出是否符合题目要求\r\n");
    printf("3. 可以修改TF卡中的config.ini文件测试不同值\r\n");
    printf("4. 删除config.ini文件测试错误处理\r\n");
}

// 快速测试宏
#define CONFIG_QUICK_TEST() do { \
    printf("\r\n=== config.ini快速测试 ===\r\n"); \
    printf("当前配置值: Ratio=%.2f, Limit=%.2f\r\n", get_config_ratio(), get_config_limit()); \
    printf("请通过串口发送 'conf' 命令测试\r\n"); \
    printf("========================\r\n"); \
} while(0)

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行验证：
 * 
 * 1. complete_config_verification();    // 完整验证
 * 2. quick_verify_config_function();    // 快速验证
 * 3. verify_output_format();            // 格式验证
 * 4. CONFIG_QUICK_TEST();               // 快速测试宏
 * 
 * 然后通过串口发送 "conf" 命令进行实际测试
 * 
 * 期望的交互完全符合题目要求：
 * 
 * 输入: conf
 * 输出: config.ini file not found.  (文件不存在时)
 * 
 * 输入: conf
 * 输出: Ratio = 1.99             (文件存在时)
 *       Limit = 10.11
 *       config read success
 */
