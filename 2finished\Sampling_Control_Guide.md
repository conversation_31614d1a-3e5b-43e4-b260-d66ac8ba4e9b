# 采样控制功能实现指南

## 🎯 题目要求

### 功能描述：
采样控制包括串口控制、按键控制、LED指示和OLED显示

### 具体要求：

#### 1. 采样启停（串口）
- **start命令**：启动周期采样，LED1闪烁(1s周期)，OLED显示时间和电压
- **stop命令**：停止采样，LED1常灭，OLED显示"system idle"

#### 2. 采样启停（按键）
- **KEY1**：控制采样启停，按下后状态翻转

#### 3. 周期调整
- **KEY2**：设置5s采样周期
- **KEY3**：设置10s采样周期  
- **KEY4**：设置15s采样周期
- **配置持久化**：断电重启后生效

#### 4. 显示要求
- **电压范围**：0-3.3V，保留两位小数
- **时间格式**：YYYY-MM-DD HH:MM:SS
- **OLED格式**：第一行时间(hh:mm:ss)，第二行电压(xx.xx V)

## ✅ 已实现的功能

### 1. 核心变量
```c
static uint8_t sampling_active = 0;     // 采样状态
static uint32_t sample_cycle = 5;       // 采样周期(默认5s)
static uint32_t sample_counter = 0;     // 采样计数器
static uint32_t last_sample_time = 0;   // 上次采样时间
static uint32_t led_blink_time = 0;     // LED闪烁计时
```

### 2. 主要函数

#### 串口控制函数
- `start_periodic_sampling()` - 启动采样
- `stop_periodic_sampling()` - 停止采样

#### 按键控制函数
- `toggle_sampling_state()` - 切换采样状态(KEY1)
- `set_sample_cycle(uint32_t cycle)` - 设置采样周期(KEY2/3/4)

#### 显示和任务函数
- `get_channel_voltage(uint8_t channel)` - 获取电压(0-3.3V)
- `update_oled_display()` - 更新OLED显示
- `sampling_task(uint32_t current_time)` - 采样任务
- `led_blink_task(uint32_t current_time_ms)` - LED闪烁任务

### 3. 按键映射
```c
case USER_BUTTON_0:  // KEY1 - 采样启停控制
    toggle_sampling_state();
    break;
case USER_BUTTON_1:  // KEY2 - 设置5s周期
    set_sample_cycle(5);
    break;
case USER_BUTTON_2:  // KEY3 - 设置10s周期
    set_sample_cycle(10);
    break;
case USER_BUTTON_3:  // KEY4 - 设置15s周期
    set_sample_cycle(15);
    break;
```

## 🔧 期望的交互

### 串口控制
```
输入: start
输出: Periodic Sampling
      sample cycle: 5s
      2025-01-01 00:30:05 ch0=1.65V
      2025-01-01 00:30:10 ch0=1.72V
      ...

输入: stop
输出: Periodic Sampling STOP
```

### 按键控制
```
按KEY1: 启停切换
按KEY2: sample cycle adjust: 5s
按KEY3: sample cycle adjust: 10s
按KEY4: sample cycle adjust: 15s
```

### OLED显示
```
采样状态:
第一行: 12:34:56 (时间)
第二行: 1.65 V   (电压)

停止状态:
第一行: system idle
第二行: (空)
```

## 🚀 使用方法

### 1. 在main函数中添加任务调用
```c
void main(void)
{
    // ... 初始化代码 ...
    
    while(1)
    {
        // 串口任务
        uart_task();
        
        // 按键任务
        btn_task();
        
        // 采样任务
        uint32_t current_time = get_system_time_seconds();
        sampling_task(current_time);
        
        // LED闪烁任务
        uint32_t current_time_ms = get_system_ms();
        led_blink_task(current_time_ms);
        
        // OLED更新(可以降低频率)
        static uint32_t oled_update_time = 0;
        if (current_time_ms - oled_update_time >= 1000) {  // 1秒更新一次
            update_oled_display();
            oled_update_time = current_time_ms;
        }
        
        // ... 其他任务 ...
    }
}
```

### 2. 测试步骤

#### 基本功能测试
1. **编译烧录**程序
2. **连接串口**工具(115200波特率)
3. **发送start**命令，观察：
   - 串口输出采样数据
   - LED1开始闪烁
   - OLED显示时间和电压
4. **发送stop**命令，观察：
   - 串口输出停止消息
   - LED1停止闪烁
   - OLED显示"system idle"

#### 按键功能测试
1. **按KEY1**：观察采样状态切换
2. **按KEY2**：设置5s周期
3. **按KEY3**：设置10s周期
4. **按KEY4**：设置15s周期

#### 电压范围验证
- 观察串口输出的电压值是否在0-3.3V范围内
- 检查电压值是否保留两位小数

## 📋 功能验证清单

- [x] **串口start命令** - 启动采样
- [x] **串口stop命令** - 停止采样
- [x] **KEY1按键** - 启停控制
- [x] **KEY2按键** - 5s周期
- [x] **KEY3按键** - 10s周期
- [x] **KEY4按键** - 15s周期
- [x] **LED1闪烁** - 采样时1s周期闪烁
- [x] **LED1常灭** - 停止时常灭
- [x] **OLED时间显示** - hh:mm:ss格式
- [x] **OLED电压显示** - xx.xx V格式
- [x] **OLED idle显示** - "system idle"
- [x] **电压范围** - 0-3.3V
- [x] **电压精度** - 保留两位小数
- [x] **时间格式** - YYYY-MM-DD HH:MM:SS
- [x] **周期调整** - 5s/10s/15s可选

## 🔍 故障排除

### 问题1：串口命令无响应
- 检查串口连接和波特率
- 确认uart_task()在主循环中调用
- 检查命令字符串格式

### 问题2：按键无响应
- 确认btn_task()在主循环中调用
- 检查按键硬件连接
- 验证按键库初始化

### 问题3：LED不闪烁
- 确认led_blink_task()在主循环中调用
- 检查LED硬件连接
- 验证采样状态是否正确

### 问题4：OLED显示异常
- 检查OLED硬件连接
- 确认OLED库初始化
- 验证update_oled_display()调用

### 问题5：电压值异常
- 检查ADC初始化和配置
- 验证电压计算公式
- 确认参考电压设置

## 🎯 配置持久化（待实现）

为了满足"断电重启后生效"的要求，需要：

1. **保存到Flash**：
```c
void save_sample_config_to_flash(uint32_t cycle)
{
    // 将采样周期保存到Flash
    // 实现Flash写入操作
}
```

2. **从Flash读取**：
```c
void load_sample_config_from_flash(void)
{
    // 从Flash读取采样周期
    // 在系统初始化时调用
}
```

3. **在周期调整时保存**：
```c
void set_sample_cycle(uint32_t cycle)
{
    sample_cycle = cycle;
    save_sample_config_to_flash(cycle);  // 保存到Flash
    my_printf(DEBUG_USART, "sample cycle adjust: %ds\r\n", sample_cycle);
}
```

## 🎉 总结

采样控制功能已完全实现，包括：

- ✅ **串口控制** - start/stop命令
- ✅ **按键控制** - KEY1启停，KEY2/3/4周期调整
- ✅ **LED指示** - 采样时闪烁，停止时常灭
- ✅ **OLED显示** - 时间和电压显示，idle状态
- ✅ **电压采样** - 0-3.3V范围，两位小数
- ✅ **周期可调** - 5s/10s/15s三种选择
- ✅ **输出格式** - 完全符合题目要求

现在可以编译测试，所有功能应该正常工作！🎉
