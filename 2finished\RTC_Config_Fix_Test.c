/*
 * RTC配置功能修复测试
 * 解决"RTC Config failed"问题的完整测试
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试RTC配置修复
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_rtc_config_fix(void)
{
    printf("\r\n=== RTC配置功能修复测试 ===\r\n");
    
    printf("🔧 修复内容：\r\n");
    printf("1. ✅ 移除重复的RTC配置模式处理逻辑\r\n");
    printf("2. ✅ 统一使用process_rtc_time_input函数\r\n");
    printf("3. ✅ 添加rtc_basic_init()确保RTC基础配置\r\n");
    printf("4. ✅ 使用全局预分频器配置(prescaler_a/prescaler_s)\r\n");
    printf("5. ✅ 添加延时确保RTC稳定\r\n");
    printf("6. ✅ 完善时间范围验证\r\n");
    printf("\r\n");
    
    printf("📋 测试步骤：\r\n");
    printf("1. 输入: RTC Config\r\n");
    printf("   期望: Input Datetime\r\n");
    printf("\r\n");
    printf("2. 输入: 2025-01-01 15:00:10\r\n");
    printf("   期望: RTC Config success         Time:2025-01-01 15:00:10\r\n");
    printf("\r\n");
    printf("3. 输入: RTC now\r\n");
    printf("   期望: Current Time:2025-01-01 15:00:xx\r\n");
    printf("\r\n");
}

/*!
    \brief      测试RTC配置流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_rtc_config_process(void)
{
    printf("\r\n=== 测试RTC配置流程 ===\r\n");
    
    printf("步骤1: 模拟RTC Config命令\r\n");
    process_uart_command("RTC Config");
    
    printf("\r\n步骤2: 模拟时间输入\r\n");
    process_uart_command("2025-01-01 15:00:10");
    
    printf("\r\n步骤3: 模拟RTC now命令\r\n");
    process_uart_command("RTC now");
    
    printf("\r\n=== RTC配置流程测试完成 ===\r\n");
}

/*!
    \brief      测试边界条件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_rtc_boundary_conditions(void)
{
    printf("\r\n=== 测试RTC边界条件 ===\r\n");
    
    printf("测试1: 有效时间范围\r\n");
    process_uart_command("RTC Config");
    process_uart_command("2025-12-31 23:59:59");
    
    printf("\r\n测试2: 无效年份\r\n");
    process_uart_command("RTC Config");
    process_uart_command("1999-01-01 12:00:00");
    
    printf("\r\n测试3: 无效月份\r\n");
    process_uart_command("RTC Config");
    process_uart_command("2025-13-01 12:00:00");
    
    printf("\r\n测试4: 无效时间格式\r\n");
    process_uart_command("RTC Config");
    process_uart_command("invalid format");
    
    printf("\r\n=== 边界条件测试完成 ===\r\n");
}

/*!
    \brief      检查RTC配置状态
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_rtc_config_status(void)
{
    printf("\r\n=== 检查RTC配置状态 ===\r\n");
    
    // 检查RTC时钟源
    uint32_t bdctl = RCU_BDCTL;
    printf("RTC时钟源状态:\r\n");
    printf("- RTCEN: %s\r\n", (bdctl & RCU_BDCTL_RTCEN) ? "已使能" : "未使能");
    printf("- RTCSRC: %d\r\n", (bdctl >> 8) & 0x3);
    
    // 检查预分频器
    extern uint32_t prescaler_a, prescaler_s;
    printf("预分频器配置:\r\n");
    printf("- prescaler_a: 0x%02X\r\n", prescaler_a);
    printf("- prescaler_s: 0x%02X\r\n", prescaler_s);
    
    // 检查当前时间
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);
    printf("当前RTC时间:\r\n");
    printf("- 年: 0x%02X\r\n", rtc_time.year);
    printf("- 月: 0x%02X\r\n", rtc_time.month);
    printf("- 日: 0x%02X\r\n", rtc_time.date);
    printf("- 时: 0x%02X\r\n", rtc_time.hour);
    printf("- 分: 0x%02X\r\n", rtc_time.minute);
    printf("- 秒: 0x%02X\r\n", rtc_time.second);
    
    printf("\r\n=== RTC状态检查完成 ===\r\n");
}

/*!
    \brief      RTC配置问题诊断
    \param[in]  none
    \param[out] none
    \retval     none
*/
void diagnose_rtc_config_issues(void)
{
    printf("\r\n=== RTC配置问题诊断 ===\r\n");
    
    printf("🔍 常见问题及解决方案：\r\n");
    printf("\r\n1. RTC Config failed - 可能原因：\r\n");
    printf("   - RTC时钟源未正确配置\r\n");
    printf("   - 预分频器设置错误\r\n");
    printf("   - 备份域写保护未解除\r\n");
    printf("   - RTC初始化模式进入失败\r\n");
    printf("\r\n");
    
    printf("2. 解决方案：\r\n");
    printf("   ✅ 调用rtc_basic_init()确保基础配置\r\n");
    printf("   ✅ 使用全局预分频器配置\r\n");
    printf("   ✅ 添加延时确保RTC稳定\r\n");
    printf("   ✅ 完善错误处理和验证\r\n");
    printf("\r\n");
    
    printf("3. 验证方法：\r\n");
    printf("   - 检查RTC寄存器状态\r\n");
    printf("   - 验证时钟源配置\r\n");
    printf("   - 测试时间设置和读取\r\n");
    printf("\r\n");
}

/*!
    \brief      完整的RTC配置测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_rtc_config_test(void)
{
    printf("\r\n🚀 开始完整的RTC配置测试\r\n");
    
    // 1. 修复说明
    test_rtc_config_fix();
    
    // 2. 状态检查
    check_rtc_config_status();
    
    // 3. 配置流程测试
    test_rtc_config_process();
    
    // 4. 边界条件测试
    test_rtc_boundary_conditions();
    
    // 5. 问题诊断
    diagnose_rtc_config_issues();
    
    printf("\r\n✅ RTC配置测试完成！\r\n");
    printf("现在应该可以正常使用RTC Config功能了。\r\n");
}
