

#ifndef DEVICE_INFO_H
#define DEVICE_INFO_H

#include "stdint.h"
#include "stdbool.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Device information constants */
#define DEVICE_ID_STRING        "2025-CIMC-137766"
#define DEVICE_ID_LENGTH        16
#define FLASH_DEVICE_ID_ADDR    0x08000000  /* Flash address to store device ID */

/* Device information structure */
typedef struct {
    char device_id[DEVICE_ID_LENGTH + 1];  /* Device ID string */
    uint32_t flash_id;                      /* Flash chip ID */
    uint32_t mcu_id[3];                     /* MCU unique ID */
    char firmware_version[16];              /* Firmware version */
} device_info_t;

/* Function declarations */
void device_info_init(void);
const char* get_device_id(void);
uint32_t get_flash_id(void);
void get_mcu_unique_id(uint32_t* id);
const char* get_firmware_version(void);
void print_device_info(void);

#ifdef __cplusplus
}
#endif

#endif /* DEVICE_INFO_H */
