/*!
    \file    system_selftest.c
    \brief   system self-test module implementation
    
    \version 2025-06-15, V1.0.0, system self-test for GD32F470VET6
*/

/*
    Copyright (c) 2025, MCUSTUDIO

    All rights reserved.
    Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors
       may be used to endorse or promote products derived from this software without
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
*/

#include "mcu_cmic_gd32f470vet6.h"
#include "system_selftest.h"
#include "usart_app.h"  // 包含日志记录函数声明

/* 全局自检报告 */
static selftest_report_t g_selftest_report;

/*!
    \brief      初始化系统自检模块
    \param[in]  none
    \param[out] none
    \retval     none
*/
void system_selftest_init(void)
{
    /* 清空自检报告 */
    memset(&g_selftest_report, 0, sizeof(selftest_report_t));
    
    /* 初始化测试项目名称 */
    strcpy(g_selftest_report.flash_test.name, "flash");
    strcpy(g_selftest_report.tfcard_test.name, "TF card");
    strcpy(g_selftest_report.rtc_test.name, "RTC");
    
    g_selftest_report.test_count = 3;
    g_selftest_report.pass_count = 0;
}

/*!
    \brief      运行系统自检
    \param[in]  none
    \param[out] none
    \retval     none
*/
void system_selftest_run(void)
{
    /* 重置通过计数 */
    g_selftest_report.pass_count = 0;

    /* 记录系统测试开始日志 */
    write_log_entry("system hardware test");

    /* 开始自检 */
    my_printf(DEBUG_USART, "====== system selftest======\r\n");

    /* Flash自检 */
    if (selftest_flash(&g_selftest_report.flash_test) == SELFTEST_OK) {
        g_selftest_report.pass_count++;
    }

    /* TF卡自检 */
    if (selftest_tfcard(&g_selftest_report.tfcard_test) == SELFTEST_OK) {
        g_selftest_report.pass_count++;
    }

    /* RTC自检 */
    if (selftest_rtc(&g_selftest_report.rtc_test) == SELFTEST_OK) {
        g_selftest_report.pass_count++;
    }

    /* 打印自检报告 */
    selftest_print_report(&g_selftest_report);

    /* 记录系统测试结果日志 */
    if (g_selftest_report.pass_count == g_selftest_report.test_count) {
        write_log_entry("test ok");
    } else {
        // 记录具体的错误信息
        if (g_selftest_report.tfcard_test.result == SELFTEST_ERROR) {
            write_log_entry("test error: tf card not found");
        } else {
            write_log_entry("test error");
        }
    }

    my_printf(DEBUG_USART, "====== system selftest======\r\n");
}

/*!
    \brief      Flash自检
    \param[in]  item: 自检项目结构体指针
    \param[out] none
    \retval     selftest_result_t: 自检结果
*/
selftest_result_t selftest_flash(selftest_item_t* item)
{
    uint32_t flash_id = 0;

    if (item == NULL) {
        return SELFTEST_ERROR;
    }

    /* 读取Flash ID */
    flash_id = spi_flash_read_id();

    /* 检查Flash ID是否有效 */
    if (flash_id != 0x00000000 && flash_id != 0xFFFFFFFF) {
        item->result = SELFTEST_OK;
        snprintf(item->detail, sizeof(item->detail), "flash ID:0x%06X", flash_id);
        return SELFTEST_OK;
    } else {
        item->result = SELFTEST_ERROR;
        snprintf(item->detail, sizeof(item->detail), "can not read flash ID");
        return SELFTEST_ERROR;
    }
}

/*!
    \brief      TF卡自检
    \param[in]  item: 自检项目结构体指针
    \param[out] none
    \retval     selftest_result_t: 自检结果
*/
selftest_result_t selftest_tfcard(selftest_item_t* item)
{
    sd_error_enum sd_status;
    uint32_t card_capacity = 0;

    if (item == NULL) {
        return SELFTEST_ERROR;
    }

    /* 尝试初始化SD卡 */
    sd_status = sd_init();

    if (sd_status == SD_OK) {
        /* 获取SD卡容量 */
        card_capacity = sd_card_capacity_get();

        item->result = SELFTEST_OK;
        snprintf(item->detail, sizeof(item->detail), "TF card memory: %lu KB", card_capacity);
        return SELFTEST_OK;
    } else {
        item->result = SELFTEST_ERROR;
        snprintf(item->detail, sizeof(item->detail), "can not find TF card");
        return SELFTEST_ERROR;
    }
}

/*!
    \brief      RTC自检
    \param[in]  item: 自检项目结构体指针
    \param[out] none
    \retval     selftest_result_t: 自检结果
*/
selftest_result_t selftest_rtc(selftest_item_t* item)
{
    if (item == NULL) {
        return SELFTEST_ERROR;
    }

    /* 使用软件RTC读取时间 */
    extern void software_rtc_get_time(int *year, int *month, int *day, int *hour, int *minute, int *second);

    int year, month, day, hour, minute, second;
    software_rtc_get_time(&year, &month, &day, &hour, &minute, &second);

    /* 检查RTC时间是否合理 */
    if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59 && second >= 0 && second <= 59) {
        item->result = SELFTEST_OK;
        snprintf(item->detail, sizeof(item->detail),
                "RTC:%04d-%02d-%02d %02d:%02d:%02d",
                year, month, day, hour, minute, second);
        return SELFTEST_OK;
    } else {
        item->result = SELFTEST_ERROR;
        snprintf(item->detail, sizeof(item->detail), "RTC time invalid");
        return SELFTEST_ERROR;
    }
}

/*!
    \brief      打印自检报告
    \param[in]  report: 自检报告结构体指针
    \param[out] none
    \retval     none
*/
void selftest_print_report(const selftest_report_t* report)
{
    if (report == NULL) {
        return;
    }

    /* 按照要求的格式打印测试结果 */

    /* 打印Flash测试结果 */
    if (report->flash_test.result == SELFTEST_OK) {
        my_printf(DEBUG_USART, "flash....................ok\r\n");
    } else {
        my_printf(DEBUG_USART, "flash....................error\r\n");
    }

    /* 打印TF卡测试结果 */
    if (report->tfcard_test.result == SELFTEST_OK) {
        my_printf(DEBUG_USART, "TF card..................ok\r\n");
    } else {
        my_printf(DEBUG_USART, "TF card..................error\r\n");
    }

    /* 打印详细信息 */
    my_printf(DEBUG_USART, "%s\r\n", report->flash_test.detail);
    if (report->tfcard_test.result == SELFTEST_ERROR) {
        my_printf(DEBUG_USART, "can not find TF card\r\n");
    } else {
        my_printf(DEBUG_USART, "%s\r\n", report->tfcard_test.detail);
    }
    my_printf(DEBUG_USART, "%s\r\n", report->rtc_test.detail);
}
