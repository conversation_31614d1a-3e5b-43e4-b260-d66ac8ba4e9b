/*
 * 配置文件读取功能最终测试
 * 验证conf命令完整功能
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试配置文件读取功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_config_file_reading(void)
{
    printf("\r\n========================================\r\n");
    printf("配置文件读取功能测试\r\n");
    printf("========================================\r\n");
    
    printf("✅ 已实现功能:\r\n");
    printf("1. 专业INI解析器 (状态机设计)\r\n");
    printf("2. conf命令处理\r\n");
    printf("3. 精确的输出格式\r\n");
    printf("4. Flash配置保存\r\n");
    printf("5. 完整错误处理\r\n");
    printf("\r\n");
    
    printf("📋 支持的配置文件格式:\r\n");
    printf("[Ratio]\r\n");
    printf("Ch0 = 1.99\r\n");
    printf("\r\n");
    printf("[Limit]\r\n");
    printf("Ch0 = 10.11\r\n");
    printf("\r\n");
    
    printf("🎯 命令使用方法:\r\n");
    printf("通过串口发送: conf\r\n");
    printf("\r\n");
    
    printf("📤 期望输出:\r\n");
    printf("文件不存在时:\r\n");
    printf("  config. ini file not found.\r\n");
    printf("\r\n");
    printf("文件存在时:\r\n");
    printf("  Ratio = 1.99\r\n");
    printf("  Limit= 10.11\r\n");
    printf("  config read success\r\n");
    printf("\r\n");
    
    printf("🔧 技术特点:\r\n");
    printf("- 使用f_read逐字符读取，兼容性好\r\n");
    printf("- 状态机解析，支持节和键值对\r\n");
    printf("- 自动去除空格和处理注释\r\n");
    printf("- 浮点数解析和验证\r\n");
    printf("- SPI Flash持久化存储\r\n");
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("✅ 配置文件读取功能实现完成！\r\n");
    printf("现在可以通过串口测试 'conf' 命令\r\n");
    printf("========================================\r\n");
}

/*!
    \brief      创建示例配置文件
    \param[in]  none
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int create_example_config(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    printf("创建示例配置文件...\r\n");
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        result = f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        if (result == FR_OK) {
            printf("示例配置文件创建成功\r\n");
            printf("现在可以测试 'conf' 命令\r\n");
            return 0;
        }
    }
    
    printf("配置文件创建失败\r\n");
    return -1;
}

/*!
    \brief      演示conf命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void demo_conf_command(void)
{
    printf("\r\n=== conf命令演示 ===\r\n");
    
    printf("1. 测试文件不存在:\r\n");
    f_unlink("0:/config.ini");
    printf("执行: conf\r\n");
    process_uart_command("conf");
    printf("\r\n");
    
    printf("2. 创建配置文件并测试:\r\n");
    if (create_example_config() == 0) {
        printf("执行: conf\r\n");
        process_uart_command("conf");
        
        printf("\r\n验证配置值:\r\n");
        printf("当前变比: %.2f\r\n", get_current_ratio());
        printf("当前阈值: %.2f\r\n", get_current_limit());
    }
    
    printf("\r\n=== 演示完成 ===\r\n");
}
