/*
 * 配置文件读取专门调试程序
 * 帮助宝宝排查config.ini文件读取问题
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      详细调试config.ini文件读取
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_config_ini_detailed(void)
{
    printf("\r\n=== 详细调试config.ini文件读取 ===\r\n");
    
    FIL config_file;
    FRESULT result;
    UINT bytes_read;
    char buffer[256];
    
    // 1. 尝试打开文件
    printf("1. 尝试打开文件 '0:/config.ini'...\r\n");
    result = f_open(&config_file, "0:/config.ini", FA_READ);
    
    if (result != FR_OK) {
        printf("❌ 文件打开失败，错误代码: %d\r\n", result);
        
        // 尝试其他路径
        printf("尝试其他路径...\r\n");
        const char* alt_paths[] = {
            "config.ini",
            "0:config.ini", 
            "/config.ini",
            "CONFIG.INI",
            "0:/CONFIG.INI",
            NULL
        };
        
        for (int i = 0; alt_paths[i] != NULL; i++) {
            printf("  尝试: %s\r\n", alt_paths[i]);
            result = f_open(&config_file, alt_paths[i], FA_READ);
            if (result == FR_OK) {
                printf("  ✅ 成功打开: %s\r\n", alt_paths[i]);
                break;
            } else {
                printf("  ❌ 失败，错误: %d\r\n", result);
            }
        }
        
        if (result != FR_OK) {
            printf("所有路径都失败，请检查:\r\n");
            printf("1. SD卡是否正确插入\r\n");
            printf("2. 文件是否存在于根目录\r\n");
            printf("3. 文件名是否完全匹配\r\n");
            return;
        }
    } else {
        printf("✅ 文件打开成功\r\n");
    }
    
    // 2. 读取文件大小
    DWORD file_size = f_size(&config_file);
    printf("2. 文件大小: %d 字节\r\n", file_size);
    
    if (file_size == 0) {
        printf("❌ 文件为空！\r\n");
        f_close(&config_file);
        return;
    }
    
    // 3. 读取整个文件内容
    printf("3. 读取文件内容:\r\n");
    result = f_read(&config_file, buffer, sizeof(buffer)-1, &bytes_read);
    
    if (result == FR_OK) {
        buffer[bytes_read] = '\0';
        printf("✅ 成功读取 %d 字节\r\n", bytes_read);
        printf("文件内容:\r\n");
        printf("--- 开始 ---\r\n");
        
        // 显示文件内容，包括不可见字符
        for (int i = 0; i < bytes_read; i++) {
            char c = buffer[i];
            if (c >= 32 && c <= 126) {
                printf("%c", c);
            } else if (c == '\r') {
                printf("\\r");
            } else if (c == '\n') {
                printf("\\n\r\n");
            } else if (c == '\t') {
                printf("\\t");
            } else {
                printf("[%02X]", (unsigned char)c);
            }
        }
        printf("\r\n--- 结束 ---\r\n");
    } else {
        printf("❌ 文件读取失败，错误: %d\r\n", result);
    }
    
    f_close(&config_file);
    
    // 4. 测试解析函数
    printf("4. 测试解析函数:\r\n");
    test_config_parsing();
    
    printf("=== 详细调试完成 ===\r\n");
}

/*!
    \brief      测试配置解析函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_config_parsing(void)
{
    printf("测试解析各行内容:\r\n");
    
    // 模拟配置文件的各行
    const char* test_lines[] = {
        "[Ratio]",
        "Ch0 = 1.99",
        "",
        "[Limit]", 
        "Ch0 = 10.11",
        NULL
    };
    
    char current_section[32] = "";
    extern float config_ratio, config_limit;
    
    // 重置配置值
    config_ratio = 0.0f;
    config_limit = 0.0f;
    
    for (int i = 0; test_lines[i] != NULL; i++) {
        printf("  解析行 %d: '%s'\r\n", i+1, test_lines[i]);
        
        // 创建可修改的副本
        char line_copy[128];
        strcpy(line_copy, test_lines[i]);
        
        // 调用解析函数
        parse_config_ini_line(line_copy, current_section);
        
        printf("    当前节: '%s'\r\n", current_section);
        printf("    config_ratio: %.2f\r\n", config_ratio);
        printf("    config_limit: %.2f\r\n", config_limit);
    }
    
    printf("最终解析结果:\r\n");
    printf("  Ratio = %.2f (期望: 1.99)\r\n", config_ratio);
    printf("  Limit = %.2f (期望: 10.11)\r\n", config_limit);
    
    if (config_ratio == 1.99f && config_limit == 10.11f) {
        printf("✅ 解析函数工作正常\r\n");
    } else {
        printf("❌ 解析函数有问题\r\n");
    }
}

/*!
    \brief      模拟完整的conf命令执行
    \param[in]  none
    \param[out] none
    \retval     none
*/
void simulate_conf_command(void)
{
    printf("\r\n=== 模拟conf命令执行 ===\r\n");
    
    printf("执行 process_conf_command()...\r\n");
    process_conf_command();
    
    printf("=== conf命令执行完成 ===\r\n");
}

/*!
    \brief      检查SD卡文件系统状态
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_sd_filesystem_status(void)
{
    printf("\r\n=== 检查SD卡文件系统状态 ===\r\n");
    
    // 检查挂载状态
    extern FATFS fs;
    printf("1. 检查文件系统挂载...\r\n");
    
    FRESULT result = f_mount(0, &fs);
    if (result == FR_OK) {
        printf("✅ 文件系统挂载成功\r\n");
        
        // 获取文件系统信息
        DWORD free_clusters;
        FATFS* fs_ptr = &fs;
        result = f_getfree("0:", &free_clusters, &fs_ptr);
        
        if (result == FR_OK) {
            DWORD total_sectors = (fs.n_fatent - 2) * fs.csize;
            DWORD free_sectors = free_clusters * fs.csize;
            
            printf("文件系统信息:\r\n");
            printf("  总扇区: %d\r\n", total_sectors);
            printf("  空闲扇区: %d\r\n", free_sectors);
            printf("  扇区大小: %d 字节\r\n", 512);
            printf("  总容量: %d KB\r\n", total_sectors / 2);
            printf("  可用容量: %d KB\r\n", free_sectors / 2);
        }
    } else {
        printf("❌ 文件系统挂载失败，错误: %d\r\n", result);
    }
    
    printf("=== 文件系统状态检查完成 ===\r\n");
}

/*!
    \brief      完整的配置文件调试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_config_file_debug(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        配置文件读取完整调试          #\r\n");
    printf("##########################################\r\n");
    
    // 1. 检查SD卡文件系统
    check_sd_filesystem_status();
    
    // 2. 详细调试config.ini文件
    debug_config_ini_detailed();
    
    // 3. 模拟conf命令执行
    simulate_conf_command();
    
    printf("\r\n##########################################\r\n");
    printf("#        调试完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n💡 根据调试结果的解决方案:\r\n");
    printf("1. 如果文件打开失败:\r\n");
    printf("   - 检查SD卡是否正确插入\r\n");
    printf("   - 确认文件在根目录下\r\n");
    printf("   - 检查文件名大小写\r\n");
    printf("\r\n");
    printf("2. 如果文件读取失败:\r\n");
    printf("   - 检查SD卡是否损坏\r\n");
    printf("   - 重新格式化SD卡为FAT32\r\n");
    printf("   - 重新创建config.ini文件\r\n");
    printf("\r\n");
    printf("3. 如果解析失败:\r\n");
    printf("   - 检查文件编码（应为ASCII）\r\n");
    printf("   - 检查文件格式是否正确\r\n");
    printf("   - 确保没有多余的BOM字符\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_config_file_debug();
 * 
 * 这会帮助你：
 * 1. 检查SD卡文件系统状态
 * 2. 详细调试config.ini文件读取
 * 3. 测试配置解析函数
 * 4. 模拟完整的conf命令执行
 * 
 * 宝宝，你的config.ini文件格式是完全正确的：
 * [Ratio]
 * Ch0 = 1.99
 * 
 * [Limit]
 * Ch0 = 10.11
 * 
 * 问题可能在于：
 * 1. 文件路径
 * 2. SD卡挂载
 * 3. 文件编码
 * 4. 硬件连接
 * 
 * 运行这个调试程序，告诉我结果！
 */
