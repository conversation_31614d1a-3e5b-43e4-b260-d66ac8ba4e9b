/*
 * 命令调试测试程序
 * 用于诊断为什么limit命令没有被正确识别
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试命令字符串处理
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_command_string_processing(void)
{
    printf("\r\n=== 命令字符串处理测试 ===\r\n");
    
    // 测试各种可能的输入格式
    const char* test_commands[] = {
        "limit",
        "limit\r",
        "limit\n",
        "limit\r\n",
        " limit",
        "limit ",
        " limit ",
        "LIMIT",
        "Limit",
        NULL
    };
    
    for (int i = 0; test_commands[i] != NULL; i++) {
        char test_cmd[32];
        strcpy(test_cmd, test_commands[i]);
        
        printf("测试命令 %d: [", i+1);
        // 显示每个字符的ASCII码
        for (int j = 0; j < strlen(test_cmd); j++) {
            printf("%c(%d)", test_cmd[j], (int)test_cmd[j]);
            if (j < strlen(test_cmd) - 1) printf(",");
        }
        printf("]\r\n");
        
        // 模拟命令处理
        printf("处理结果: ");
        process_uart_command(test_cmd);
        printf("\r\n");
    }
    
    printf("=== 测试完成 ===\r\n");
}

/*!
    \brief      手动测试limit命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void manual_test_limit_command(void)
{
    printf("\r\n=== 手动测试limit命令 ===\r\n");
    
    // 直接调用limit命令处理函数
    printf("1. 直接调用 process_limit_command():\r\n");
    process_limit_command();
    printf("\r\n");
    
    // 通过命令处理函数调用
    printf("2. 通过 process_uart_command(\"limit\"):\r\n");
    process_uart_command("limit");
    printf("\r\n");
    
    // 测试字符串比较
    printf("3. 测试字符串比较:\r\n");
    const char* test_str = "limit";
    printf("strcmp(\"%s\", \"limit\") = %d\r\n", test_str, strcmp(test_str, "limit"));
    printf("strlen(\"%s\") = %d\r\n", test_str, strlen(test_str));
    printf("\r\n");
    
    // 测试当前阈值获取
    printf("4. 测试当前阈值获取:\r\n");
    printf("get_current_limit() = %.2f\r\n", get_current_limit());
    
    printf("=== 手动测试完成 ===\r\n");
}

/*!
    \brief      检查函数是否正确链接
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_function_linkage(void)
{
    printf("\r\n=== 检查函数链接 ===\r\n");
    
    // 检查各个函数是否可以调用
    printf("1. 检查 process_limit_command 函数:\r\n");
    if (process_limit_command != NULL) {
        printf("   ✓ 函数指针有效\r\n");
        printf("   调用结果: ");
        process_limit_command();
    } else {
        printf("   ✗ 函数指针无效\r\n");
    }
    printf("\r\n");
    
    printf("2. 检查 get_current_limit 函数:\r\n");
    if (get_current_limit != NULL) {
        printf("   ✓ 函数指针有效\r\n");
        printf("   返回值: %.2f\r\n", get_current_limit());
    } else {
        printf("   ✗ 函数指针无效\r\n");
    }
    printf("\r\n");
    
    printf("3. 检查 process_limit_input 函数:\r\n");
    if (process_limit_input != NULL) {
        printf("   ✓ 函数指针有效\r\n");
        printf("   测试调用: ");
        process_limit_input("50.0");
    } else {
        printf("   ✗ 函数指针无效\r\n");
    }
    
    printf("\r\n=== 检查完成 ===\r\n");
}

/*!
    \brief      模拟串口接收测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void simulate_uart_receive_test(void)
{
    printf("\r\n=== 模拟串口接收测试 ===\r\n");
    
    // 模拟不同的接收情况
    extern uint8_t rxbuffer[512];
    extern uint8_t rx_flag;
    
    printf("1. 模拟接收 'limit' 命令:\r\n");
    
    // 清空缓冲区
    memset(rxbuffer, 0, 512);
    
    // 模拟接收 "limit\r\n"
    strcpy((char*)rxbuffer, "limit\r\n");
    rx_flag = 1;
    
    printf("   rxbuffer内容: [%s]\r\n", rxbuffer);
    printf("   rx_flag: %d\r\n", rx_flag);
    
    // 调用uart_task处理
    printf("   调用 uart_task():\r\n");
    uart_task();
    
    printf("\r\n2. 模拟接收 'limit' 命令（无换行）:\r\n");
    
    // 清空缓冲区
    memset(rxbuffer, 0, 512);
    
    // 模拟接收 "limit"
    strcpy((char*)rxbuffer, "limit");
    rx_flag = 1;
    
    printf("   rxbuffer内容: [%s]\r\n", rxbuffer);
    printf("   调用 uart_task():\r\n");
    uart_task();
    
    printf("\r\n=== 模拟测试完成 ===\r\n");
}

/*!
    \brief      完整的命令调试程序
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_command_debug(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        命令调试完整程序              #\r\n");
    printf("##########################################\r\n");
    
    // 1. 检查函数链接
    check_function_linkage();
    
    // 2. 手动测试limit命令
    manual_test_limit_command();
    
    // 3. 测试命令字符串处理
    test_command_string_processing();
    
    // 4. 模拟串口接收测试
    simulate_uart_receive_test();
    
    printf("\r\n##########################################\r\n");
    printf("#        调试完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n调试建议：\r\n");
    printf("1. 检查上述输出中的DEBUG信息\r\n");
    printf("2. 确认limit命令是否被正确识别\r\n");
    printf("3. 检查字符串比较结果\r\n");
    printf("4. 验证函数调用是否正常\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_command_debug();
 * 
 * 然后通过串口发送 "limit" 命令，观察DEBUG输出：
 * - 应该看到 "DEBUG: Received command: [limit], length: 5"
 * - 应该看到 "DEBUG: limit command matched"
 * - 如果看到 "DEBUG: Command comparison failed"，说明字符串比较有问题
 * 
 * 根据DEBUG输出确定问题所在：
 * 1. 如果命令长度不是5，说明有多余字符
 * 2. 如果字符串比较失败，可能是编码问题
 * 3. 如果函数调用失败，可能是链接问题
 */
