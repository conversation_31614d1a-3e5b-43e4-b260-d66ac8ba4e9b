/*
 * 简单的limit命令测试
 * 用于快速验证limit命令是否工作
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      简单测试limit命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void simple_limit_test(void)
{
    printf("\r\n=== 简单limit命令测试 ===\r\n");
    
    printf("1. 直接调用limit处理函数:\r\n");
    process_limit_command();
    printf("\r\n");
    
    printf("2. 通过命令处理调用:\r\n");
    process_uart_command("limit");
    printf("\r\n");
    
    printf("3. 测试阈值输入:\r\n");
    process_limit_input("50.12");
    printf("当前阈值: %.2f\r\n", get_current_limit());
    printf("\r\n");
    
    printf("4. 测试错误输入:\r\n");
    process_limit_input("510.12");
    printf("当前阈值: %.2f\r\n", get_current_limit());
    printf("\r\n");
    
    printf("=== 测试完成 ===\r\n");
    printf("如果上述测试正常，说明limit功能本身没问题\r\n");
    printf("问题可能在于串口命令识别\r\n");
}

/*!
    \brief      测试所有命令识别
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_all_commands(void)
{
    printf("\r\n=== 测试所有命令识别 ===\r\n");
    
    const char* commands[] = {
        "ratio",
        "limit", 
        "conf",
        "RTC Config",
        "RTC now",
        "unknown",
        NULL
    };
    
    for (int i = 0; commands[i] != NULL; i++) {
        printf("测试命令: %s\r\n", commands[i]);
        process_uart_command((char*)commands[i]);
        printf("\r\n");
    }
    
    printf("=== 命令测试完成 ===\r\n");
}

/*!
    \brief      检查编译和链接
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_compilation_linkage(void)
{
    printf("\r\n=== 检查编译和链接 ===\r\n");
    
    // 检查limit相关函数是否存在
    printf("检查limit函数:\r\n");
    
    // 1. process_limit_command
    printf("1. process_limit_command: ");
    process_limit_command();
    printf("✓ 可调用\r\n");
    
    // 2. process_limit_input  
    printf("2. process_limit_input: ");
    process_limit_input("100.0");
    printf("✓ 可调用\r\n");
    
    // 3. get_current_limit
    printf("3. get_current_limit: %.2f ✓ 可调用\r\n", get_current_limit());
    
    printf("\r\n所有limit相关函数都正常链接\r\n");
    printf("=== 检查完成 ===\r\n");
}

/*!
    \brief      完整的简单测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_simple_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        limit命令简单测试             #\r\n");
    printf("##########################################\r\n");
    
    // 1. 检查编译链接
    check_compilation_linkage();
    
    // 2. 简单功能测试
    simple_limit_test();
    
    // 3. 所有命令测试
    test_all_commands();
    
    printf("\r\n##########################################\r\n");
    printf("#        简单测试完成                  #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n现在请通过串口发送以下命令测试:\r\n");
    printf("1. 发送: limit\r\n");
    printf("2. 观察是否有DEBUG输出\r\n");
    printf("3. 检查是否显示阈值和输入提示\r\n");
    printf("\r\n如果仍然显示'Unknown command'，请检查:\r\n");
    printf("1. 串口发送的字符是否正确\r\n");
    printf("2. 是否有多余的空格或特殊字符\r\n");
    printf("3. 字符编码是否正确\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_simple_test();
 * 
 * 这会直接测试limit功能，绕过串口命令解析
 * 如果这个测试正常，说明limit功能本身没问题
 * 问题在于串口命令识别部分
 * 
 * 然后通过串口发送 "limit" 命令，观察DEBUG输出：
 * - 应该看到 "DEBUG: Received command: [limit], length: 5"
 * - 应该看到 "DEBUG: limit command matched"
 * 
 * 如果看到 "Unknown command: limit"，说明字符串比较失败
 * 可能的原因：
 * 1. 字符串中有不可见字符
 * 2. 字符编码问题
 * 3. 内存问题
 */
