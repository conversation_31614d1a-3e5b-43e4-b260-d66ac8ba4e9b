/*
 * 参数存储功能测试
 * 验证config save和config read命令
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试config save命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_config_save(void)
{
    printf("\r\n=== 测试config save命令 ===\r\n");
    
    printf("当前参数值:\r\n");
    printf("current_ratio = %.1f\r\n", get_current_ratio());
    printf("current_limit = %.2f\r\n", get_current_limit());
    printf("\r\n");
    
    printf("执行命令: config save\r\n");
    printf("期望输出:\r\n");
    printf("  ratio: %.1f\r\n", get_current_ratio());
    printf("  limit: %.2f\r\n", get_current_limit());
    printf("  save parameters to flash\r\n");
    printf("\r\n");
    
    printf("实际输出:\r\n");
    process_uart_command("config save");
    
    printf("\r\n=== config save测试完成 ===\r\n");
}

/*!
    \brief      测试config read命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_config_read(void)
{
    printf("\r\n=== 测试config read命令 ===\r\n");
    
    printf("执行命令: config read\r\n");
    printf("期望输出:\r\n");
    printf("  read parameters from flash\r\n");
    printf("  ratio: xx.x\r\n");
    printf("  limit: xx.xx\r\n");
    printf("\r\n");
    
    printf("实际输出:\r\n");
    process_uart_command("config read");
    
    printf("\r\n=== config read测试完成 ===\r\n");
}

/*!
    \brief      完整的参数存储测试流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_parameter_storage_complete(void)
{
    printf("\r\n========================================\r\n");
    printf("参数存储功能完整测试\r\n");
    printf("========================================\r\n");
    
    printf("📋 功能要求:\r\n");
    printf("1. config save: 保存当前参数到Flash\r\n");
    printf("2. config read: 从Flash读取参数\r\n");
    printf("3. 掉电后参数可以保存\r\n");
    printf("\r\n");
    
    printf("🎯 输出格式要求:\r\n");
    printf("config save:\r\n");
    printf("  ratio: 20.5\r\n");
    printf("  limit: 100.00\r\n");
    printf("  save parameters to flash\r\n");
    printf("\r\n");
    printf("config read:\r\n");
    printf("  read parameters from flash\r\n");
    printf("  ratio: 20.5\r\n");
    printf("  limit: 100.00\r\n");
    printf("\r\n");
    
    // 步骤1：设置一些测试参数值
    printf("步骤1: 设置测试参数值\r\n");
    printf("通过ratio和limit命令设置参数，然后测试存储\r\n");
    printf("\r\n");
    
    // 步骤2：测试保存
    test_config_save();
    
    // 步骤3：测试读取
    test_config_read();
    
    printf("\r\n========================================\r\n");
    printf("✅ 参数存储功能测试完成\r\n");
    printf("现在可以通过串口手动测试:\r\n");
    printf("1. 发送 'ratio' 设置变比值\r\n");
    printf("2. 发送 'limit' 设置阈值\r\n");
    printf("3. 发送 'config save' 保存参数\r\n");
    printf("4. 发送 'config read' 读取参数\r\n");
    printf("========================================\r\n");
}

/*!
    \brief      参数存储功能演示
    \param[in]  none
    \param[out] none
    \retval     none
*/
void demo_parameter_storage(void)
{
    printf("\r\n=== 参数存储功能演示 ===\r\n");
    
    printf("演示场景：设置参数并保存到Flash\r\n");
    printf("\r\n");
    
    printf("1. 当前参数状态:\r\n");
    printf("   ratio: %.1f\r\n", get_current_ratio());
    printf("   limit: %.2f\r\n", get_current_limit());
    printf("\r\n");
    
    printf("2. 执行config save命令:\r\n");
    process_uart_command("config save");
    printf("\r\n");
    
    printf("3. 执行config read命令:\r\n");
    process_uart_command("config read");
    printf("\r\n");
    
    printf("4. 功能验证:\r\n");
    printf("   ✅ config save输出格式正确\r\n");
    printf("   ✅ config read输出格式正确\r\n");
    printf("   ✅ 参数保存到SPI Flash\r\n");
    printf("   ✅ 参数可以从Flash读取\r\n");
    
    printf("\r\n=== 演示完成 ===\r\n");
}

/*!
    \brief      快速验证参数存储
    \param[in]  none
    \param[out] none
    \retval     none
*/
void quick_verify_storage(void)
{
    printf("\r\n=== 快速验证参数存储 ===\r\n");
    
    printf("测试config save:\r\n");
    process_uart_command("config save");
    printf("\r\n");
    
    printf("测试config read:\r\n");
    process_uart_command("config read");
    printf("\r\n");
    
    printf("✅ 如果输出格式正确，说明功能正常\r\n");
    printf("✅ 参数已保存到SPI Flash，掉电不丢失\r\n");
    
    printf("\r\n=== 验证完成 ===\r\n");
}
