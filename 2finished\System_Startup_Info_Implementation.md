# 系统上电启动信息实现

## 功能概述
实现了系统上电后的完整启动信息显示，包括串口打印和OLED显示。

## 实现的功能

### 1.1 系统初始化开始提示
- **位置**: 系统上电后，基础外设初始化完成时
- **输出**: `====system init====`
- **实现**: 在USART初始化后立即打印

### 1.2 设备ID读取和显示
- **位置**: 系统初始化过程中
- **输出**: `Device_ID:2025-CIMC-137766`
- **实现**: 通过设备信息管理模块获取

### 1.3 系统就绪提示
- **位置**: 所有初始化完成后
- **输出**: `====system ready====`
- **实现**: 在调度器启动前打印

### 1.4 OLED状态显示
- **位置**: OLED第一行
- **显示**: `system idle`
- **实现**: 使用OLED_ShowStr函数显示

## 新增文件

### 1. sysFunction/device_info.h
设备信息管理头文件，包含：
- 设备ID常量定义
- 设备信息结构体
- 函数声明

### 2. sysFunction/device_info.c
设备信息管理实现文件，包含：
- 设备信息初始化
- 设备ID获取
- Flash ID读取
- MCU唯一ID获取
- 固件版本管理

## 修改的文件

### 1. USER/src/main.c
主要修改：
```c
// 添加头文件包含
#include "device_info.h"

// 系统初始化流程
int main(void)
{
    // 基础初始化
    systick_config();
    init_cycle_counter(false);
    delay_ms(200);
    
    // 基础外设初始化
    bsp_led_init();
    bsp_btn_init();
    bsp_usart_init();
    
    // 打印系统初始化开始
    delay_ms(100);
    my_printf(DEBUG_USART, "====system init====\r\n");
    
    // 其他外设初始化
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_adc_init();
    bsp_dac_init();
    bsp_rtc_init();
    
    // 初始化设备信息模块
    device_info_init();
    
    // 文件系统和测试
    sd_fatfs_init();
    OLED_Init();
    test_spi_flash();
    sd_fatfs_test();
    
    // 打印设备ID
    my_printf(DEBUG_USART, "Device_ID:%s\r\n", get_device_id());
    
    // 打印系统就绪
    my_printf(DEBUG_USART, "====system ready====\r\n");
    
    // OLED显示系统状态
    OLED_Clear();
    OLED_ShowStr(0, 0, "system idle", 8);
    
    // 启动调度器
    system_selftest_init();
    scheduler_init();
    while(1) {
        scheduler_run();
    }
}
```

### 2. MDK/2025137766-code.uvprojx
添加了新的源文件到项目：
```xml
<File>
  <FileName>device_info.c</FileName>
  <FileType>1</FileType>
  <FilePath>..\sysFunction\device_info.c</FilePath>
</File>
```

## 预期输出效果

### 串口输出序列
```
====system init====
Device_ID:2025-CIMC-137766
====system ready====
```

### OLED显示
```
第一行: system idle
```

## 技术特点

### 1. 模块化设计
- 设备信息管理独立成模块
- 便于维护和扩展
- 统一的接口规范

### 2. 启动时序控制
- 确保USART先初始化再打印
- 合理的延时控制
- 有序的初始化流程

### 3. 信息完整性
- 设备ID固定标识
- 系统状态清晰显示
- 启动过程可追踪

### 4. 扩展性
- 支持添加更多设备信息
- 可扩展启动信息内容
- 便于调试和维护

## 设备信息模块功能

### 核心功能
1. **设备ID管理**: 固定的设备标识符
2. **Flash ID读取**: 获取Flash芯片ID
3. **MCU唯一ID**: 读取MCU的唯一标识
4. **固件版本**: 管理固件版本信息

### 接口函数
```c
void device_info_init(void);           // 初始化设备信息
const char* get_device_id(void);       // 获取设备ID
uint32_t get_flash_id(void);           // 获取Flash ID
void get_mcu_unique_id(uint32_t* id);  // 获取MCU唯一ID
const char* get_firmware_version(void); // 获取固件版本
void print_device_info(void);          // 打印完整设备信息
```

## 测试验证

### 编译验证
1. 清理项目
2. 重新编译
3. 检查无错误和警告

### 功能验证
1. 下载程序到开发板
2. 打开串口调试工具（115200波特率）
3. 复位开发板
4. 观察串口输出序列
5. 检查OLED显示内容

### 预期结果
- ✅ 串口按序输出启动信息
- ✅ 设备ID正确显示
- ✅ OLED第一行显示"system idle"
- ✅ 系统正常进入运行状态

## 故障排除

### 串口无输出
1. 检查USART初始化
2. 检查波特率设置
3. 检查串口线连接

### OLED无显示
1. 检查OLED初始化
2. 检查I2C连接
3. 检查OLED电源

### 设备ID错误
1. 检查device_info模块编译
2. 检查常量定义
3. 检查函数调用

现在系统启动时会按照要求的顺序显示完整的启动信息。
