/*
 * 配置文件读取调试助手
 * 帮助排查为什么读不到config.ini文件
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      调试版本的conf命令处理
    \param[in]  none
    \param[out] none
    \retval     none
*/
void debug_conf_command(void)
{
    printf("\r\n=== 调试conf命令 ===\r\n");
    
    // 1. 检查文件系统挂载
    extern FATFS fs;
    printf("1. 检查文件系统挂载...\r\n");
    FRESULT mount_result = f_mount(0, &fs);
    printf("f_mount(0, &fs) = %d\r\n", mount_result);
    
    if (mount_result != FR_OK) {
        printf("❌ 文件系统挂载失败\r\n");
        return;
    }
    printf("✅ 文件系统挂载成功\r\n");
    
    // 2. 尝试打开配置文件
    FIL config_file;
    printf("\r\n2. 尝试打开config.ini文件...\r\n");
    FRESULT open_result = f_open(&config_file, "0:/config.ini", FA_READ);
    printf("f_open(&file, \"0:/config.ini\", FA_READ) = %d\r\n", open_result);
    
    if (open_result != FR_OK) {
        printf("❌ 文件打开失败\r\n");
        
        // 尝试不同的路径
        printf("\r\n3. 尝试其他路径...\r\n");
        open_result = f_open(&config_file, "config.ini", FA_READ);
        printf("f_open(&file, \"config.ini\", FA_READ) = %d\r\n", open_result);
        
        if (open_result != FR_OK) {
            printf("❌ 所有路径都失败\r\n");
            printf("请确认:\r\n");
            printf("1. TF卡已插入\r\n");
            printf("2. config.ini文件存在于TF卡根目录\r\n");
            printf("3. 文件名大小写正确\r\n");
            return;
        }
    }
    
    printf("✅ 文件打开成功\r\n");
    
    // 3. 读取文件内容
    printf("\r\n4. 读取文件内容...\r\n");
    char buffer[256];
    UINT bytes_read;
    FRESULT read_result = f_read(&config_file, buffer, sizeof(buffer)-1, &bytes_read);
    printf("f_read() = %d, 读取字节数: %d\r\n", read_result, bytes_read);
    
    if (read_result == FR_OK && bytes_read > 0) {
        buffer[bytes_read] = '\0';
        printf("✅ 文件内容:\r\n");
        printf("--- 开始 ---\r\n");
        printf("%s", buffer);
        printf("\r\n--- 结束 ---\r\n");
    } else {
        printf("❌ 文件读取失败\r\n");
    }
    
    f_close(&config_file);
    printf("\r\n=== 调试完成 ===\r\n");
}

/*!
    \brief      创建测试配置文件
    \param[in]  none
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int create_debug_config_file(void)
{
    printf("\r\n=== 创建测试配置文件 ===\r\n");
    
    // 1. 检查文件系统
    extern FATFS fs;
    FRESULT mount_result = f_mount(0, &fs);
    if (mount_result != FR_OK) {
        printf("❌ 文件系统挂载失败: %d\r\n", mount_result);
        return -1;
    }
    
    // 2. 创建配置文件
    FIL config_file;
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    FRESULT create_result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    printf("创建文件结果: %d\r\n", create_result);
    
    if (create_result == FR_OK) {
        UINT bytes_written;
        FRESULT write_result = f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        printf("写入结果: %d, 写入字节数: %d\r\n", write_result, bytes_written);
        
        f_close(&config_file);
        
        if (write_result == FR_OK) {
            printf("✅ 配置文件创建成功\r\n");
            return 0;
        }
    }
    
    printf("❌ 配置文件创建失败\r\n");
    return -1;
}

/*!
    \brief      完整的配置文件调试流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void full_config_debug(void)
{
    printf("\r\n========================================\r\n");
    printf("配置文件读取完整调试流程\r\n");
    printf("========================================\r\n");
    
    // 步骤1：检查TF卡状态
    printf("步骤1: 检查TF卡状态\r\n");
    DSTATUS disk_status = disk_initialize(0);
    printf("disk_initialize(0) = %d (0=成功)\r\n", disk_status);
    
    if (disk_status != 0) {
        printf("❌ TF卡初始化失败\r\n");
        return;
    }
    
    // 步骤2：测试文件不存在的情况
    printf("\r\n步骤2: 测试文件不存在的情况\r\n");
    f_unlink("0:/config.ini");  // 删除文件
    debug_conf_command();
    
    // 步骤3：创建文件并测试
    printf("\r\n步骤3: 创建文件并测试\r\n");
    if (create_debug_config_file() == 0) {
        debug_conf_command();
        
        // 步骤4：测试实际的conf命令
        printf("\r\n步骤4: 测试实际的conf命令\r\n");
        printf("执行process_uart_command(\"conf\"):\r\n");
        process_uart_command("conf");
    }
    
    printf("\r\n========================================\r\n");
    printf("调试流程完成\r\n");
    printf("========================================\r\n");
}

/*!
    \brief      简单的文件系统测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void simple_filesystem_test(void)
{
    printf("\r\n=== 简单文件系统测试 ===\r\n");
    
    extern FATFS fs;
    FRESULT result = f_mount(0, &fs);
    printf("文件系统挂载: %s\r\n", (result == FR_OK) ? "成功" : "失败");
    
    if (result == FR_OK) {
        // 尝试创建一个简单的测试文件
        FIL test_file;
        result = f_open(&test_file, "0:/test.txt", FA_CREATE_ALWAYS | FA_WRITE);
        printf("创建测试文件: %s\r\n", (result == FR_OK) ? "成功" : "失败");
        
        if (result == FR_OK) {
            const char* test_content = "Hello World!";
            UINT bytes_written;
            f_write(&test_file, test_content, strlen(test_content), &bytes_written);
            f_close(&test_file);
            printf("写入测试内容: %d 字节\r\n", bytes_written);
            
            // 尝试读取
            result = f_open(&test_file, "0:/test.txt", FA_READ);
            if (result == FR_OK) {
                char read_buffer[32];
                UINT bytes_read;
                f_read(&test_file, read_buffer, sizeof(read_buffer)-1, &bytes_read);
                read_buffer[bytes_read] = '\0';
                f_close(&test_file);
                printf("读取测试内容: %s\r\n", read_buffer);
            }
        }
    }
    
    printf("=== 测试完成 ===\r\n");
}
