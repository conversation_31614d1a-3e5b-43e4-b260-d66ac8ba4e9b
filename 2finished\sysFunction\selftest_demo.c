/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/15
* Note: 系统自检演示和测试代码
*/

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      演示系统自检功能
    \param[in]  none
    \param[out] none
    \retval     none
    \note       此函数用于演示和测试系统自检功能
*/
void selftest_demo(void)
{
    my_printf(DEBUG_USART, "\r\n=== 系统自检演示 ===\r\n");
    my_printf(DEBUG_USART, "请通过串口发送 'test' 命令来启动系统自检\r\n");
    my_printf(DEBUG_USART, "预期输出格式:\r\n");
    my_printf(DEBUG_USART, "====== system selftest======\r\n");
    my_printf(DEBUG_USART, "flash………………ok\r\n");
    my_printf(DEBUG_USART, "TF card………………ok\r\n");
    my_printf(DEBUG_USART, "flash ID:0xCxxxxx\r\n");
    my_printf(DEBUG_USART, "TF card memory: xxxx KB\r\n");
    my_printf(DEBUG_USART, "RTC:2025-01-01 01:00:50\r\n");
    my_printf(DEBUG_USART, "====== system selftest======\r\n");
    my_printf(DEBUG_USART, "===================\r\n\r\n");
}

/*!
    \brief      手动触发系统自检（用于调试）
    \param[in]  none
    \param[out] none
    \retval     none
*/
void manual_selftest_trigger(void)
{
    my_printf(DEBUG_USART, "\r\n手动触发系统自检...\r\n");
    system_selftest_run();
}
