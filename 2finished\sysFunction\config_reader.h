/*
 * config.ini文件读取器头文件
 * 支持读取和解析config.ini配置文件
 */

#ifndef __CONFIG_READER_H__
#define __CONFIG_READER_H__

#include "stdint.h"
#include "stdbool.h"

#ifdef __cplusplus
extern "C" {
#endif

// 配置项最大长度定义
#define CONFIG_MAX_LINE_LENGTH      128
#define CONFIG_MAX_KEY_LENGTH       32
#define CONFIG_MAX_VALUE_LENGTH     64
#define CONFIG_MAX_SECTION_LENGTH   32
#define CONFIG_MAX_ITEMS            50

// 配置项数据结构
typedef struct {
    char section[CONFIG_MAX_SECTION_LENGTH];    // 节名 [section]
    char key[CONFIG_MAX_KEY_LENGTH];            // 键名
    char value[CONFIG_MAX_VALUE_LENGTH];        // 值
} config_item_t;

// 配置文件数据结构
typedef struct {
    config_item_t items[CONFIG_MAX_ITEMS];      // 配置项数组
    uint16_t item_count;                        // 配置项数量
    bool is_loaded;                             // 是否已加载
} config_data_t;

// 函数声明
int config_load_from_file(const char* filename);
int config_load_from_sd(const char* filename);
const char* config_get_string(const char* section, const char* key, const char* default_value);
int config_get_int(const char* section, const char* key, int default_value);
float config_get_float(const char* section, const char* key, float default_value);
bool config_get_bool(const char* section, const char* key, bool default_value);
void config_print_all(void);
void config_clear(void);
int config_get_item_count(void);
bool config_is_loaded(void);

// 调试和测试函数
void config_test_parser(void);
void config_create_sample_file(void);

#ifdef __cplusplus
}
#endif

#endif /* __CONFIG_READER_H__ */
