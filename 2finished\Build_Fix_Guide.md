# 编译问题修复指南

## 问题描述
编译时出现以下错误：
```
Error: L6218E: Undefined symbol system_selftest_init (referred from main.o).
Error: L6218E: Undefined symbol system_selftest_run (referred from usart_app.o).
```

## 问题原因
`system_selftest.c`文件没有被添加到Keil项目的编译列表中，导致链接时找不到相关函数的定义。

## 解决方案

### 方法1: 通过Keil IDE添加文件（推荐）
1. 在Keil MDK中打开项目 `MDK/2025137766-code.uvprojx`
2. 在项目管理器中找到 `sysFunction` 组
3. 右键点击 `sysFunction` 组，选择 "Add Existing Files to Group 'sysFunction'"
4. 浏览到 `sysFunction` 文件夹
5. 选择以下文件并添加：
   - `system_selftest.c`
   - `btn_app.c` (如果还没有添加)
6. 点击 "Add" 确认
7. 重新编译项目

### 方法2: 手动修改项目文件（已完成）
项目文件 `MDK/2025137766-code.uvprojx` 已经被修改，添加了以下文件：

```xml
<File>
  <FileName>system_selftest.c</FileName>
  <FileType>1</FileType>
  <FilePath>..\sysFunction\system_selftest.c</FilePath>
</File>
<File>
  <FileName>btn_app.c</FileName>
  <FileType>1</FileType>
  <FilePath>..\sysFunction\btn_app.c</FilePath>
</File>
```

## 验证修复

### 编译验证
1. 清理项目：Project → Clean Targets
2. 重新编译：Project → Rebuild all target files
3. 检查编译输出，应该看到：
   ```
   compiling system_selftest.c...
   compiling btn_app.c...
   ```
4. 链接应该成功，没有 "Undefined symbol" 错误

### 功能验证
1. 下载程序到开发板
2. 打开串口调试工具（115200波特率）
3. 发送 "test" 命令
4. 应该收到系统自检结果输出

## 预期编译输出
成功编译后，应该在 `MDK/output/` 目录下看到以下新文件：
- `system_selftest.o`
- `system_selftest.crf`
- `system_selftest.d`
- `btn_app.o`
- `btn_app.crf`
- `btn_app.d`

## 常见问题排除

### 问题1: 文件路径错误
**症状**: 编译时提示找不到文件
**解决**: 确保文件路径相对于项目文件是正确的：
- 项目文件位置：`MDK/2025137766-code.uvprojx`
- 源文件位置：`sysFunction/system_selftest.c`
- 相对路径：`..\sysFunction\system_selftest.c`

### 问题2: 头文件包含路径
**症状**: 编译时提示找不到头文件
**解决**: 确保包含路径中有 `sysFunction` 目录：
```
..\sysFunction
```

### 问题3: 重复定义错误
**症状**: 链接时提示函数重复定义
**解决**: 检查是否在多个文件中定义了相同的函数，移除重复定义

## 文件依赖关系

### system_selftest.c 依赖
- `mcu_cmic_gd32f470vet6.h` - BSP头文件
- `system_selftest.h` - 自检模块头文件
- `gd25qxx.h` - SPI Flash驱动
- `sdio_sdcard.h` - SD卡驱动
- `gd32f4xx_rtc.h` - RTC驱动

### 链接依赖
- `spi_flash_read_id()` - 来自 gd25qxx.c
- `sd_init()` - 来自 sdio_sdcard.c
- `sd_card_capacity_get()` - 来自 sdio_sdcard.c
- `rtc_current_time_get()` - 来自 GD32固件库
- `my_printf()` - 来自 usart_app.c

## 成功标志
编译成功后，应该看到类似以下输出：
```
Build target 'McuSTUDIO_F470VET6'
compiling system_selftest.c...
compiling btn_app.c...
linking...
Program Size: Code=xxxxx RO-data=xxxx RW-data=xxx ZI-data=xxxx
".\output\Project.axf" - 0 Error(s), 0 Warning(s).
Target created.
```

现在可以重新编译项目，应该能够成功编译并链接。
