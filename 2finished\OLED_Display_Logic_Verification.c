/*
 * OLED显示逻辑验证
 * 严格按照任务要求实现OLED显示
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      OLED显示逻辑说明
    \param[in]  none
    \param[out] none
    \retval     none
*/
void oled_display_logic_explanation(void)
{
    printf("\r\n=== OLED显示逻辑说明 ===\r\n");
    
    printf("📋 任务要求:\r\n");
    printf("\"自上电起，除了采集状态下OLED显示刷新数据外，\r\n");
    printf(" 其余时刻均第一行显示'system idle'，第二行为空\"\r\n");
    
    printf("\r\n🎯 逻辑分析:\r\n");
    printf("1. 采集状态下 = uart_flag=1 且 sampling_active=1\r\n");
    printf("   → OLED显示时间和电压\r\n");
    printf("2. 其余时刻 = 所有其他情况\r\n");
    printf("   → OLED显示'system idle'\r\n");
    
    printf("\r\n🔧 修正后的判断条件:\r\n");
    printf("if (uart_flag && sampling_active) {\r\n");
    printf("    // 采集状态：显示时间和电压\r\n");
    printf("} else {\r\n");
    printf("    // 其余时刻：显示system idle\r\n");
    printf("}\r\n");
    
    printf("\r\n=== 逻辑说明完成 ===\r\n");
}

/*!
    \brief      OLED显示状态测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_oled_display_states(void)
{
    printf("\r\n=== OLED显示状态测试 ===\r\n");
    
    printf("🧪 各种状态下的OLED显示:\r\n");
    
    printf("\r\n状态1: 系统刚上电\r\n");
    printf("  uart_flag = 0, sampling_active = 0\r\n");
    printf("  OLED显示: system idle (第一行)\r\n");
    printf("  OLED显示: 空 (第二行)\r\n");
    
    printf("\r\n状态2: 发送start命令后\r\n");
    printf("  uart_flag = 1, sampling_active = 1\r\n");
    printf("  OLED显示: 00:30:05 (第一行，时分秒)\r\n");
    printf("  OLED显示: 10.50 V (第二行，电压)\r\n");
    
    printf("\r\n状态3: 发送stop命令后\r\n");
    printf("  uart_flag = 0, sampling_active = 0\r\n");
    printf("  OLED显示: system idle (第一行)\r\n");
    printf("  OLED显示: 空 (第二行)\r\n");
    
    printf("\r\n状态4: 按KEY1停止采样\r\n");
    printf("  uart_flag = 0, sampling_active = 0\r\n");
    printf("  OLED显示: system idle (第一行)\r\n");
    printf("  OLED显示: 空 (第二行)\r\n");
    
    printf("\r\n状态5: 按KEY1启动采样\r\n");
    printf("  uart_flag = 1, sampling_active = 1\r\n");
    printf("  OLED显示: 00:30:10 (第一行，实时时间)\r\n");
    printf("  OLED显示: 12.30 V (第二行，实时电压)\r\n");
    
    printf("\r\n=== 状态测试完成 ===\r\n");
}

/*!
    \brief      OLED显示格式验证
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_oled_display_format(void)
{
    printf("\r\n=== OLED显示格式验证 ===\r\n");
    
    printf("📐 显示格式要求:\r\n");
    
    printf("\r\n采集状态下:\r\n");
    printf("  第一行: 时间（只显示时分秒，格式hh:mm:ss）\r\n");
    printf("    示例: 00:30:05, 14:25:30, 23:59:59\r\n");
    printf("    代码: snprintf(time_str, \"%%02d:%%02d:%%02d\", hour, minute, second)\r\n");
    printf("\r\n");
    printf("  第二行: 电压值（小数点后保留两位，格式xx.xx V）\r\n");
    printf("    示例: 10.50 V, 3.25 V, 15.75 V\r\n");
    printf("    代码: snprintf(voltage_str, \"%%.2f V\", voltage)\r\n");
    
    printf("\r\n停止状态下:\r\n");
    printf("  第一行: \"system idle\"\r\n");
    printf("  第二行: 空（用空格清空）\r\n");
    printf("    代码: OLED_ShowStr(0, 2, \"           \", 8)\r\n");
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*!
    \brief      OLED与串口同步验证
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_oled_serial_sync(void)
{
    printf("\r\n=== OLED与串口同步验证 ===\r\n");
    
    printf("🔄 同步要求:\r\n");
    printf("\"OLED数据与串口同步，每10s刷新一次\"\r\n");
    
    printf("\r\n同步逻辑:\r\n");
    printf("1. 每次采样输出时调用update_oled_display()\r\n");
    printf("2. OLED显示的时间和电压与串口输出一致\r\n");
    printf("3. 采样周期调整后，OLED刷新频率也相应变化\r\n");
    
    printf("\r\n示例场景:\r\n");
    printf("设置10秒周期后:\r\n");
    printf("  00:30:05 - 串口输出: 2025-01-01 00:30:05 ch0=10.50V\r\n");
    printf("           - OLED显示: 00:30:05 / 10.50 V\r\n");
    printf("  00:30:15 - 串口输出: 2025-01-01 00:30:15 ch0=12.30V\r\n");
    printf("           - OLED显示: 00:30:15 / 12.30 V\r\n");
    printf("  00:30:25 - 串口输出: 2025-01-01 00:30:25 ch0=8.75V\r\n");
    printf("           - OLED显示: 00:30:25 / 8.75 V\r\n");
    
    printf("\r\n=== 同步验证完成 ===\r\n");
}

/*!
    \brief      实际测试指南
    \param[in]  none
    \param[out] none
    \retval     none
*/
void practical_test_guide(void)
{
    printf("\r\n=== 实际测试指南 ===\r\n");
    
    printf("🧪 测试步骤:\r\n");
    
    printf("\r\n1. 上电测试:\r\n");
    printf("   操作: 系统上电\r\n");
    printf("   期望: OLED第一行显示\"system idle\"，第二行空\r\n");
    
    printf("\r\n2. 启动采样测试:\r\n");
    printf("   操作: 串口发送\"start\"\r\n");
    printf("   期望: OLED开始显示实时时间和电压\r\n");
    printf("   验证: 每5秒OLED数据更新一次\r\n");
    
    printf("\r\n3. 周期调整测试:\r\n");
    printf("   操作: 按KEY3设置10秒周期\r\n");
    printf("   期望: OLED每10秒更新一次\r\n");
    printf("   验证: 更新频率与串口输出同步\r\n");
    
    printf("\r\n4. 停止采样测试:\r\n");
    printf("   操作: 串口发送\"stop\"或按KEY1\r\n");
    printf("   期望: OLED立即回到\"system idle\"显示\r\n");
    
    printf("\r\n5. 按键控制测试:\r\n");
    printf("   操作: 按KEY1切换采样状态\r\n");
    printf("   期望: OLED显示与采样状态同步变化\r\n");
    
    printf("\r\n=== 测试指南完成 ===\r\n");
}

/*!
    \brief      OLED显示逻辑完整验证
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_oled_logic_verification(void)
{
    printf("\r\n========================================\r\n");
    printf("OLED显示逻辑完整验证\r\n");
    printf("========================================\r\n");
    
    printf("✅ 修正内容:\r\n");
    printf("1. 判断条件: if (uart_flag && sampling_active)\r\n");
    printf("2. 采集状态: 显示时间(hh:mm:ss)和电压(xx.xx V)\r\n");
    printf("3. 其余时刻: 显示\"system idle\"和空行\r\n");
    printf("4. 同步更新: 与串口输出完全同步\r\n");
    
    printf("\r\n🎯 符合任务要求:\r\n");
    printf("\"自上电起，除了采集状态下OLED显示刷新数据外，\r\n");
    printf(" 其余时刻均第一行显示'system idle'，第二行为空\"\r\n");
    
    // 执行所有验证
    oled_display_logic_explanation();
    test_oled_display_states();
    verify_oled_display_format();
    verify_oled_serial_sync();
    practical_test_guide();
    
    printf("\r\n========================================\r\n");
    printf("🎉 OLED显示逻辑修正完成！\r\n");
    printf("现在完全符合任务要求！\r\n");
    printf("========================================\r\n");
}
