/*
 * RTC功能最终测试程序
 * 基于你的底层代码实现，确保完全符合题目要求
 */

#include "mcu_cmic_gd32f470vet6.h"
#include <string.h>

// 测试RTC功能的完整流程
void test_rtc_complete_flow(void)
{
    printf("\r\n========================================\r\n");
    printf("         RTC功能完整测试\r\n");
    printf("========================================\r\n");
    
    printf("测试要求：\r\n");
    printf("2.1 串口输入\"RTC Config\"，串口返回\"Input Datetime\"\r\n");
    printf("2.2 输入当前标准时间，例如\"2025-01-01 15:00:10\"，返回指定格式\r\n");
    printf("2.3 输入\"RTC now\"，串口返回当前时间\r\n");
    printf("\r\n");
    
    printf("请按照以下步骤测试：\r\n");
    printf("1. 发送: RTC Config\r\n");
    printf("   期望: Input Datetime\r\n");
    printf("\r\n");
    printf("2. 发送: 2025-01-01 15:00:10\r\n");
    printf("   期望: RTC Config success         Time:2025-01-01 15:00:10\r\n");
    printf("\r\n");
    printf("3. 发送: RTC now\r\n");
    printf("   期望: Current Time:2025-01-01 15:00:XX\r\n");
    printf("\r\n");
    printf("========================================\r\n");
}

// 验证RTC寄存器状态
void check_rtc_registers(void)
{
    printf("\r\n=== RTC寄存器状态检查 ===\r\n");
    printf("RTC_TIME: 0x%08X\r\n", RTC_TIME);
    printf("RTC_DATE: 0x%08X\r\n", RTC_DATE);
    printf("RTC_CTL:  0x%08X\r\n", RTC_CTL);
    printf("RTC_STAT: 0x%08X\r\n", RTC_STAT);
    printf("RTC_PSC:  0x%08X\r\n", RTC_PSC);
    
    // 检查RTC是否使能
    if (RTC_CTL & RTC_CTL_ALRM0EN) {
        printf("RTC状态: 使能\r\n");
    } else {
        printf("RTC状态: 未使能\r\n");
    }
    
    printf("========================\r\n");
}

// 检查时钟源配置
void check_rtc_clock_source(void)
{
    printf("\r\n=== RTC时钟源检查 ===\r\n");
    
    uint32_t bdctl = RCU_BDCTL;
    uint32_t rtc_src = (bdctl >> 8) & 0x3;
    
    printf("RCU_BDCTL: 0x%08X\r\n", bdctl);
    printf("RTC时钟源: ");
    
    switch(rtc_src) {
        case 0:
            printf("无时钟源 ⚠️\r\n");
            break;
        case 1:
            printf("LXTAL (32.768kHz外部晶振)\r\n");
            if (RCU_BDCTL & RCU_BDCTL_LXTALSTB) {
                printf("LXTAL状态: 稳定 ✓\r\n");
            } else {
                printf("LXTAL状态: 不稳定 ⚠️\r\n");
            }
            break;
        case 2:
            printf("IRC32K (内部32kHz振荡器)\r\n");
            break;
        case 3:
            printf("HXTAL/32 (外部高速晶振32分频)\r\n");
            break;
    }
    
    // 检查RTC使能状态
    if (RCU_BDCTL & RCU_BDCTL_RTCEN) {
        printf("RTC时钟: 使能 ✓\r\n");
    } else {
        printf("RTC时钟: 未使能 ⚠️\r\n");
    }
    
    printf("==================\r\n");
}

// 测试串口接收功能
void test_uart_receive(void)
{
    extern uint8_t rx_flag;
    extern uint8_t rxbuffer[512];
    
    printf("\r\n=== 串口接收测试 ===\r\n");
    printf("rx_flag状态: %d\r\n", rx_flag);
    printf("rxbuffer地址: 0x%08X\r\n", (uint32_t)rxbuffer);
    
    // 检查USART配置
    printf("USART0状态: ");
    if (USART_CTL0(USART0) & USART_CTL0_UEN) {
        printf("使能 ✓\r\n");
    } else {
        printf("未使能 ⚠️\r\n");
    }
    
    printf("USART0波特率寄存器: 0x%08X\r\n", USART_BAUD(USART0));
    printf("USART0控制寄存器0: 0x%08X\r\n", USART_CTL0(USART0));
    printf("USART0状态寄存器: 0x%08X\r\n", USART_STAT0(USART0));
    
    printf("==================\r\n");
}

// 手动测试RTC设置功能
void manual_test_rtc_setting(void)
{
    printf("\r\n=== 手动RTC设置测试 ===\r\n");
    
    // 模拟"RTC Config"命令
    printf("模拟命令: RTC Config\r\n");
    process_uart_command("RTC Config");
    
    // 等待一下
    for(volatile int i = 0; i < 1000000; i++);
    
    // 模拟时间输入
    printf("模拟时间输入: 2025-06-15 14:30:25\r\n");
    process_uart_command("2025-06-15 14:30:25");
    
    // 等待一下
    for(volatile int i = 0; i < 1000000; i++);
    
    // 模拟"RTC now"命令
    printf("模拟命令: RTC now\r\n");
    process_uart_command("RTC now");
    
    printf("===================\r\n");
}

// 检查调度器任务
void check_scheduler_tasks(void)
{
    printf("\r\n=== 调度器任务检查 ===\r\n");
    printf("uart_task应该在调度器中运行\r\n");
    printf("检查调度器是否正常调用uart_task\r\n");
    printf("====================\r\n");
}

// 完整的系统诊断
void rtc_system_diagnosis(void)
{
    printf("\r\n##########################################\r\n");
    printf("#          RTC系统完整诊断             #\r\n");
    printf("##########################################\r\n");
    
    // 1. 检查时钟源
    check_rtc_clock_source();
    
    // 2. 检查RTC寄存器
    check_rtc_registers();
    
    // 3. 检查串口接收
    test_uart_receive();
    
    // 4. 检查调度器
    check_scheduler_tasks();
    
    // 5. 手动测试RTC功能
    manual_test_rtc_setting();
    
    // 6. 显示测试说明
    test_rtc_complete_flow();
    
    printf("\r\n##########################################\r\n");
    printf("#          诊断完成                    #\r\n");
    printf("##########################################\r\n");
}

// 简化的RTC功能验证
void simple_rtc_verification(void)
{
    printf("\r\n=== 简化RTC功能验证 ===\r\n");
    
    // 直接调用RTC相关函数进行验证
    extern rtc_parameter_struct rtc_initpara;
    
    printf("1. 检查RTC初始化状态\r\n");
    if (bsp_rtc_init() == 0) {
        printf("   ✓ RTC初始化成功\r\n");
    } else {
        printf("   ⚠️ RTC初始化可能有问题\r\n");
    }
    
    printf("2. 读取当前RTC时间\r\n");
    rtc_current_time_get(&rtc_initpara);
    printf("   年: 0x%02X, 月: 0x%02X, 日: 0x%02X\r\n", 
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date);
    printf("   时: 0x%02X, 分: 0x%02X, 秒: 0x%02X\r\n", 
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
    
    printf("3. 测试命令处理函数\r\n");
    printf("   调用process_uart_command函数...\r\n");
    
    printf("========================\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行测试：
 * 
 * 1. rtc_system_diagnosis();     // 完整系统诊断
 * 2. simple_rtc_verification();  // 简化功能验证
 * 
 * 然后通过串口发送以下命令进行实际测试：
 * - "RTC Config" -> 应该返回 "Input Datetime"
 * - "2025-01-01 15:00:10" -> 应该返回 "RTC Config success         Time:2025-01-01 15:00:10"
 * - "RTC now" -> 应该返回 "Current Time:2025-01-01 15:00:XX"
 */
