/*
 * 配置文件读取简单调试
 * 帮助排查读不到config.ini的问题
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      简单的配置文件读取测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void simple_config_test(void)
{
    printf("\r\n=== 简单配置文件读取测试 ===\r\n");
    
    // 步骤1：检查TF卡
    printf("1. 检查TF卡状态...\r\n");
    extern FATFS fs;
    FRESULT result = f_mount(0, &fs);
    printf("f_mount结果: %d (0=成功)\r\n", result);
    
    if (result != FR_OK) {
        printf("❌ TF卡挂载失败，请检查:\r\n");
        printf("   - TF卡是否插入\r\n");
        printf("   - TF卡是否格式化为FAT32\r\n");
        printf("   - 硬件连接是否正常\r\n");
        return;
    }
    
    // 步骤2：尝试读取文件
    printf("\r\n2. 尝试读取config.ini...\r\n");
    FIL config_file;
    result = f_open(&config_file, "0:/config.ini", FA_READ);
    printf("f_open结果: %d (0=成功)\r\n", result);
    
    if (result != FR_OK) {
        printf("❌ 文件打开失败，可能原因:\r\n");
        printf("   - 文件不存在\r\n");
        printf("   - 文件名错误（注意大小写）\r\n");
        printf("   - 路径错误\r\n");
        
        // 尝试创建文件
        printf("\r\n3. 尝试创建测试文件...\r\n");
        result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
        if (result == FR_OK) {
            const char* test_content = 
                "[Ratio]\r\n"
                "Ch0 = 1.99\r\n"
                "\r\n"
                "[Limit]\r\n"
                "Ch0 = 10.11\r\n";
            
            UINT bytes_written;
            f_write(&config_file, test_content, strlen(test_content), &bytes_written);
            f_close(&config_file);
            printf("✅ 测试文件创建成功，写入%d字节\r\n", bytes_written);
            
            // 再次尝试读取
            printf("\r\n4. 再次尝试读取...\r\n");
            result = f_open(&config_file, "0:/config.ini", FA_READ);
            if (result == FR_OK) {
                printf("✅ 文件读取成功\r\n");
                f_close(&config_file);
            }
        } else {
            printf("❌ 无法创建文件，错误: %d\r\n", result);
        }
    } else {
        printf("✅ 文件打开成功\r\n");
        f_close(&config_file);
    }
    
    printf("\r\n=== 测试完成 ===\r\n");
}

/*!
    \brief      测试conf命令的详细版本
    \param[in]  none
    \param[out] none
    \retval     none
*/
void detailed_conf_test(void)
{
    printf("\r\n=== 详细conf命令测试 ===\r\n");
    
    printf("执行process_uart_command(\"conf\"):\r\n");
    printf("--- 开始 ---\r\n");
    process_uart_command("conf");
    printf("--- 结束 ---\r\n");
    
    printf("\r\n当前配置值:\r\n");
    printf("current_ratio = %.2f\r\n", get_current_ratio());
    printf("current_limit = %.2f\r\n", get_current_limit());
    
    printf("\r\n=== 测试完成 ===\r\n");
}

/*!
    \brief      完整的故障排除流程
    \param[in]  none
    \param[out] none
    \retval     none
*/
void troubleshoot_config_reading(void)
{
    printf("\r\n========================================\r\n");
    printf("配置文件读取故障排除\r\n");
    printf("========================================\r\n");
    
    printf("问题：读不到config.ini文件\r\n");
    printf("可能原因分析：\r\n");
    printf("1. TF卡未插入或损坏\r\n");
    printf("2. 文件系统挂载失败\r\n");
    printf("3. config.ini文件不存在\r\n");
    printf("4. 文件路径或名称错误\r\n");
    printf("5. 文件格式问题\r\n");
    printf("\r\n");
    
    // 执行简单测试
    simple_config_test();
    
    // 执行详细测试
    detailed_conf_test();
    
    printf("\r\n========================================\r\n");
    printf("故障排除建议：\r\n");
    printf("1. 确保TF卡已正确插入\r\n");
    printf("2. 确保TF卡格式化为FAT32\r\n");
    printf("3. 在TF卡根目录创建config.ini文件\r\n");
    printf("4. 文件内容格式：\r\n");
    printf("   [Ratio]\r\n");
    printf("   Ch0 = 1.99\r\n");
    printf("   \r\n");
    printf("   [Limit]\r\n");
    printf("   Ch0 = 10.11\r\n");
    printf("5. 注意文件名大小写\r\n");
    printf("========================================\r\n");
}

/*!
    \brief      快速验证功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void quick_verify(void)
{
    printf("\r\n=== 快速验证 ===\r\n");
    
    printf("发送conf命令:\r\n");
    process_uart_command("conf");
    
    printf("\r\n如果看到 'config. ini file not found.':\r\n");
    printf("1. 检查TF卡是否插入\r\n");
    printf("2. 检查config.ini文件是否存在\r\n");
    printf("3. 运行 troubleshoot_config_reading() 进行详细诊断\r\n");
    
    printf("\r\n如果看到 'Ratio = xxx, Limit= xxx, config read success':\r\n");
    printf("✅ 功能正常工作！\r\n");
    
    printf("\r\n=== 验证完成 ===\r\n");
}
