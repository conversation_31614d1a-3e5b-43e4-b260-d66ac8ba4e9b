/*-----------------------------------------------------------------------*/
/* Unicode support functions for FatFs                                  */
/*-----------------------------------------------------------------------*/

#include "ff.h"

#if _USE_LFN

/*-----------------------------------------------------------------------*/
/* Unicode to OEM code conversion                                       */
/*-----------------------------------------------------------------------*/

WCHAR ff_convert (	/* Converted character, Returns zero on error */
	WCHAR	src,	/* Character code to be converted */
	UINT	dir		/* 0: Unicode to OEMCP, 1: OEMCP to Unicode */
)
{
	WCHAR c;

	if (src < 0x80) {	/* ASCII */
		c = src;
	} else {
		if (dir) {		/* OEMCP to Unicode */
			/* Simple conversion for Latin-1 characters */
			c = src;
		} else {		/* Unicode to OEMCP */
			/* Simple conversion for Latin-1 characters */
			c = (src < 0x100) ? src : '?';
		}
	}

	return c;
}

/*-----------------------------------------------------------------------*/
/* Unicode upper case conversion                                         */
/*-----------------------------------------------------------------------*/

WCHAR ff_wtoupper (	/* Upper converted character */
	WCHAR chr		/* Input character */
)
{
	/* Simple ASCII upper case conversion */
	if (chr >= 'a' && chr <= 'z') {
		return chr - 0x20;
	}
	
	/* Extended Latin-1 upper case conversion */
	if (chr >= 0xE0 && chr <= 0xFE && chr != 0xF7) {
		return chr - 0x20;
	}
	
	return chr;
}

#endif /* _USE_LFN */
