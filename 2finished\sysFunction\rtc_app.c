#include "mcu_cmic_gd32f470vet6.h"

// 外部声明RTC参数结构体变量（在BSP层定义）
extern rtc_parameter_struct rtc_initpara;

// 时区偏移配置（小时）- 默认为0，可根据需要调整
// 例如：如果工具是东八区(UTC+8)，而需要显示UTC时间，则设置为-8
static int8_t timezone_offset = 0;

// RTC配置等待输入标志
static uint8_t waiting_for_datetime_input = 0;

/**
 * @brief 十进制转BCD码
 * @param dec 十进制数值
 * @return BCD码
 */
static uint8_t dec_to_bcd(uint8_t dec)
{
    return ((dec / 10) << 4) | (dec % 10);
}

/**
 * @brief 设置时区偏移
 * @param offset 时区偏移（小时），正数为东时区，负数为西时区
 */
void rtc_set_timezone_offset(int8_t offset)
{
    timezone_offset = offset;
}

/**
 * @brief 获取当前时区偏移
 * @return 时区偏移（小时）
 */
int8_t rtc_get_timezone_offset(void)
{
    return timezone_offset;
}

/**
 * @brief 应用时区偏移到时间
 * @param time_param 输入的时间参数
 * @param adjusted_param 输出的调整后时间参数
 */
void rtc_apply_timezone_offset(rtc_parameter_struct *time_param, rtc_parameter_struct *adjusted_param)
{
    // 复制原始时间
    *adjusted_param = *time_param;

    if (timezone_offset == 0)
        return; // 无偏移，直接返回

    // BCD转十进制进行计算
    int hour = ((time_param->hour >> 4) * 10) + (time_param->hour & 0x0F);
    int day = ((time_param->date >> 4) * 10) + (time_param->date & 0x0F);
    // int month = ((time_param->month >> 4) * 10) + (time_param->month & 0x0F); // 暂时未使用
    // int year = ((time_param->year >> 4) * 10) + (time_param->year & 0x0F) + 2000; // 暂时未使用

    // 应用时区偏移
    hour += timezone_offset;

    // 处理小时溢出
    if (hour >= 24)
    {
        hour -= 24;
        day += 1;
        // 简化处理：这里可以添加更复杂的日期计算逻辑
    }
    else if (hour < 0)
    {
        hour += 24;
        day -= 1;
        // 简化处理：这里可以添加更复杂的日期计算逻辑
    }

    // 转换回BCD格式
    adjusted_param->hour = dec_to_bcd(hour);
    adjusted_param->date = dec_to_bcd(day);
}

/**
 * @brief 计算星期几 (Zeller公式)
 * @param year 年份
 * @param month 月份
 * @param day 日期
 * @return 星期几 (RTC_MONDAY=1, RTC_SUNDAY=7)
 */
static uint8_t calculate_weekday(int year, int month, int day)
{
    if (month < 3)
    {
        month += 12;
        year--;
    }
    int k = year % 100;
    int j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    // Zeller公式: 0=周六, 1=周日, 2=周一, ..., 6=周五
    // 转换为GD32格式: 1=周一, 2=周二, ..., 7=周日
    int weekday = ((h + 5) % 7) + 1;
    return (uint8_t)weekday;
}

/**
 * @brief RTC基础初始化 - 配置时钟源和使能RTC
 */
void rtc_basic_init(void)
{
    extern void rcu_periph_clock_enable(rcu_periph_enum periph);
    extern void rcu_rtc_clock_config(uint32_t rtc_clock_source);
    extern void rcu_osci_on(rcu_osci_type_enum osci);
    extern ErrStatus rcu_osci_stab_wait(rcu_osci_type_enum osci);

    // 1. 使能PWR时钟
    rcu_periph_clock_enable(RCU_PMU);

    // 2. 使能备份域写访问权限 (关键步骤!)
    pmu_backup_write_enable();

    // 3. 检查RTC是否已经配置过
    uint32_t bdctl = RCU_BDCTL;
    if ((bdctl & RCU_BDCTL_RTCEN) == 0) {
        // RTC未使能，需要重新配置

        // 4. 启动LSI时钟并等待稳定
        rcu_osci_on(RCU_IRC32K);
        if (rcu_osci_stab_wait(RCU_IRC32K) != SUCCESS) {
            // LSI启动失败，尝试使用LXTAL
            rcu_osci_on(RCU_LXTAL);
            rcu_osci_stab_wait(RCU_LXTAL);
            rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);
        } else {
            // LSI启动成功
            rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);
        }

        // 5. 使能RTC时钟
        rcu_periph_clock_enable(RCU_RTC);
    }

    // 6. 等待RTC寄存器同步
    rtc_register_sync_wait();
}

/**
 * @brief RTC时间设置功能
 * @param time_str 时间字符串，格式：2025-01-01 12:00:30
 */
void rtc_time_set(const char *time_str)
{
    extern ErrStatus rtc_init(rtc_parameter_struct * rtc_initpara_struct);        // RTC初始化函数
    extern void rtc_current_time_get(rtc_parameter_struct * rtc_initpara_struct); // RTC时间获取函数

    int year, month, day, hour, minute, second;

    // 解析时间字符串
    if (sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second) == 6)
    {
        // 验证时间范围
        if (year >= 2000 && year <= 2099 &&
            month >= 1 && month <= 12 &&
            day >= 1 && day <= 31 &&
            hour >= 0 && hour <= 23 &&
            minute >= 0 && minute <= 59 &&
            second >= 0 && second <= 59)
        {
            // 确保RTC基础配置已完成
            rtc_basic_init();

            // 添加延时确保RTC稳定
            delay_ms(100);

            // 设置RTC参数 (使用BCD格式)
            rtc_initpara.year = dec_to_bcd(year - 2000);                    // 转换为2位年份BCD
            rtc_initpara.month = dec_to_bcd(month);                         // 月份BCD
            rtc_initpara.date = dec_to_bcd(day);                            // 日期BCD
            rtc_initpara.day_of_week = calculate_weekday(year, month, day); // 计算星期几
            rtc_initpara.hour = dec_to_bcd(hour);                           // 小时BCD
            rtc_initpara.minute = dec_to_bcd(minute);                       // 分钟BCD
            rtc_initpara.second = dec_to_bcd(second);                       // 秒BCD
            rtc_initpara.factor_asyn = 0x7F;                                // 异步预分频因子 (LSI: 32000/(127+1)=250Hz)
            rtc_initpara.factor_syn = 0xF9;                                 // 同步预分频因子 (250/(249+1)=1Hz)
            rtc_initpara.am_pm = RTC_AM;                                    // 24小时制
            rtc_initpara.display_format = RTC_24HOUR;                       // 24小时显示格式

							// 配置RTC
						if (rtc_init(&rtc_initpara) == SUCCESS)
					{
					my_printf(DEBUG_USART, "RTC Config success\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n");  // 确保这里有换行
					my_printf(DEBUG_USART, "Time: %04d-%02d-%02d %02d:%02d:%02d\r\n", 
						year, month, day, hour, minute, second);
						}
							else
            {
                // RTC初始化失败，不输出错误信息
            }
        }
        else
        {
            // 时间范围无效，不输出错误信息
        }
    }
    else
    {
        // 时间格式无效，不输出错误信息
    }
}

/**
 * @brief 处理RTC时间设置命令
 * @param command_buffer 命令缓冲区
 */
void rtc_handle_time_commands(uint8_t *command_buffer)
{
    char *rtc_config_pos = NULL;
    char *time_start = NULL;

    // 检查是否包含"RTC Config"命令
    rtc_config_pos = strstr((char *)command_buffer, "RTC Config");
    if (rtc_config_pos != NULL)
    {
        // 查找"RTC Config"后面的时间字符串（跳过"RTC Config "）
        time_start = rtc_config_pos + strlen("RTC Config");

        // 跳过空格
        while (*time_start == ' ' && *time_start != '\0')
        {
            time_start++;
        }

        // 检查时间格式是否正确（年份以20开头，包含日期分隔符-和时间分隔符:或-）
        if (strstr(time_start, "20") == time_start &&                             // 确保以20开头
            strstr(time_start, "-") != NULL &&                                    // 包含日期分隔符
            (strstr(time_start, ":") != NULL || strstr(time_start, "-") != NULL)) // 包含时间分隔符:或-
        {
            // 处理时间格式：将 01-30-10 转换为 01:30:10
            char formatted_time[32];
            char *src = time_start;
            char *dst = formatted_time;
            int dash_count = 0;

            // 复制并转换格式
            while (*src != '\0' && *src != '\r' && *src != '\n' && (dst - formatted_time) < sizeof(formatted_time) - 1)
            {
                if (*src == '-')
                {
                    dash_count++;
                    if (dash_count <= 2) // 前两个-保留（日期部分）
                    {
                        *dst++ = *src;
                    }
                    else // 第三个-及以后转换为:（时间部分）
                    {
                        *dst++ = ':';
                    }
                }
                else
                {
                    *dst++ = *src;
                }
                src++;
            }
            *dst = '\0';
            rtc_time_set(formatted_time); // 设置RTC时间
        }
        else
        {
            // 格式无效，不输出错误信息
        }
    }
}

// 处理RTC Config命令
void rtc_handle_config_command(void)
{
    my_printf(DEBUG_USART, "Input Datetime\r\n");
    waiting_for_datetime_input = 1; // 设置等待输入标志
}

// 检查是否正在等待日期时间输入
uint8_t rtc_is_waiting_datetime_input(void)
{
    return waiting_for_datetime_input;
}

// 处理日期时间输入
uint8_t rtc_process_datetime_input(uint8_t *input_buffer)
{
    if (!waiting_for_datetime_input)
        return 0;

    waiting_for_datetime_input = 0; // 清除等待标志

    // 清理输入字符串，移除换行符和回车符
    char clean_buffer[32];
    uint8_t i = 0, j = 0;

    while (input_buffer[i] != '\0' && i < sizeof(clean_buffer) - 1)
    {
        if (input_buffer[i] != '\r' && input_buffer[i] != '\n')
        {
            clean_buffer[j++] = input_buffer[i];
        }
        i++;
    }
    clean_buffer[j] = '\0';

    // 调用RTC时间设置函数
    rtc_time_set(clean_buffer);
    return 1;
}
