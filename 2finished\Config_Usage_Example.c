/*
 * config.ini文件使用示例
 * 演示如何读取和使用配置文件
 */

#include "mcu_cmic_gd32f470vet6.h"
#include "config_reader.h"

// 应用程序配置结构体
typedef struct {
    char device_name[32];
    char version[16];
    bool debug_mode;
    int max_users;
    char ip_address[16];
    int port;
    float timeout;
    bool enable_dhcp;
    int sample_rate;
    float calibration_factor;
    bool enable_filter;
    float threshold;
} app_config_t;

static app_config_t g_app_config;

/*!
    \brief      从配置文件加载应用程序配置
    \param[in]  none
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int load_application_config(void)
{
    // 加载配置文件
    if (config_load_from_file("config.ini") != 0) {
        my_printf(DEBUG_USART, "无法加载配置文件，使用默认配置\r\n");
        return -1;
    }
    
    // 读取系统配置
    strncpy(g_app_config.device_name, 
            config_get_string("system", "device_name", "Default-Device"), 
            sizeof(g_app_config.device_name) - 1);
    
    strncpy(g_app_config.version, 
            config_get_string("system", "version", "1.0.0"), 
            sizeof(g_app_config.version) - 1);
    
    g_app_config.debug_mode = config_get_bool("system", "debug_mode", false);
    g_app_config.max_users = config_get_int("system", "max_users", 10);
    
    // 读取网络配置
    strncpy(g_app_config.ip_address, 
            config_get_string("network", "ip_address", "***********"), 
            sizeof(g_app_config.ip_address) - 1);
    
    g_app_config.port = config_get_int("network", "port", 80);
    g_app_config.timeout = config_get_float("network", "timeout", 10.0f);
    g_app_config.enable_dhcp = config_get_bool("network", "enable_dhcp", true);
    
    // 读取传感器配置
    g_app_config.sample_rate = config_get_int("sensor", "sample_rate", 100);
    g_app_config.calibration_factor = config_get_float("sensor", "calibration_factor", 1.0f);
    g_app_config.enable_filter = config_get_bool("sensor", "enable_filter", false);
    g_app_config.threshold = config_get_float("sensor", "threshold", 0.0f);
    
    my_printf(DEBUG_USART, "应用程序配置加载完成\r\n");
    return 0;
}

/*!
    \brief      打印应用程序配置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void print_application_config(void)
{
    my_printf(DEBUG_USART, "\r\n=== 应用程序配置 ===\r\n");
    
    my_printf(DEBUG_USART, "[系统配置]\r\n");
    my_printf(DEBUG_USART, "  设备名称: %s\r\n", g_app_config.device_name);
    my_printf(DEBUG_USART, "  版本: %s\r\n", g_app_config.version);
    my_printf(DEBUG_USART, "  调试模式: %s\r\n", g_app_config.debug_mode ? "开启" : "关闭");
    my_printf(DEBUG_USART, "  最大用户数: %d\r\n", g_app_config.max_users);
    
    my_printf(DEBUG_USART, "[网络配置]\r\n");
    my_printf(DEBUG_USART, "  IP地址: %s\r\n", g_app_config.ip_address);
    my_printf(DEBUG_USART, "  端口: %d\r\n", g_app_config.port);
    my_printf(DEBUG_USART, "  超时: %.1f秒\r\n", g_app_config.timeout);
    my_printf(DEBUG_USART, "  DHCP: %s\r\n", g_app_config.enable_dhcp ? "启用" : "禁用");
    
    my_printf(DEBUG_USART, "[传感器配置]\r\n");
    my_printf(DEBUG_USART, "  采样率: %d Hz\r\n", g_app_config.sample_rate);
    my_printf(DEBUG_USART, "  校准因子: %.2f\r\n", g_app_config.calibration_factor);
    my_printf(DEBUG_USART, "  滤波器: %s\r\n", g_app_config.enable_filter ? "启用" : "禁用");
    my_printf(DEBUG_USART, "  阈值: %.1f\r\n", g_app_config.threshold);
    
    my_printf(DEBUG_USART, "==================\r\n");
}

/*!
    \brief      配置文件完整测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_complete_test(void)
{
    my_printf(DEBUG_USART, "\r\n########################################\r\n");
    my_printf(DEBUG_USART, "#        配置文件完整测试              #\r\n");
    my_printf(DEBUG_USART, "########################################\r\n");
    
    // 1. 测试配置文件解析器
    config_test_parser();
    
    // 2. 加载应用程序配置
    my_printf(DEBUG_USART, "\r\n--- 加载应用程序配置 ---\r\n");
    if (load_application_config() == 0) {
        print_application_config();
    }
    
    // 3. 测试动态配置读取
    my_printf(DEBUG_USART, "\r\n--- 动态配置读取测试 ---\r\n");
    
    // 读取一些特定配置项
    const char* device_name = config_get_string("system", "device_name", "Unknown");
    int port = config_get_int("network", "port", 0);
    float calibration = config_get_float("sensor", "calibration_factor", 0.0f);
    bool debug = config_get_bool("system", "debug_mode", false);
    
    my_printf(DEBUG_USART, "动态读取结果:\r\n");
    my_printf(DEBUG_USART, "  设备名称: %s\r\n", device_name);
    my_printf(DEBUG_USART, "  网络端口: %d\r\n", port);
    my_printf(DEBUG_USART, "  校准因子: %.2f\r\n", calibration);
    my_printf(DEBUG_USART, "  调试模式: %s\r\n", debug ? "是" : "否");
    
    // 4. 测试错误处理
    my_printf(DEBUG_USART, "\r\n--- 错误处理测试 ---\r\n");
    
    const char* nonexistent = config_get_string("nonexistent", "key", "默认值");
    int invalid_int = config_get_int("invalid", "number", -999);
    
    my_printf(DEBUG_USART, "不存在的配置项: %s\r\n", nonexistent);
    my_printf(DEBUG_USART, "无效的整数配置: %d\r\n", invalid_int);
    
    my_printf(DEBUG_USART, "\r\n########################################\r\n");
    my_printf(DEBUG_USART, "#        测试完成                      #\r\n");
    my_printf(DEBUG_USART, "########################################\r\n");
}

/*!
    \brief      获取应用程序配置指针
    \param[in]  none
    \param[out] none
    \retval     应用程序配置结构体指针
*/
app_config_t* get_app_config(void)
{
    return &g_app_config;
}

/*!
    \brief      检查配置文件是否存在
    \param[in]  filename: 文件名
    \param[out] none
    \retval     true: 存在, false: 不存在
*/
bool config_file_exists(const char* filename)
{
    FIL test_file;
    FRESULT result;
    char file_path[64];
    
    snprintf(file_path, sizeof(file_path), "0:/%s", filename);
    result = f_open(&test_file, file_path, FA_READ);
    
    if (result == FR_OK) {
        f_close(&test_file);
        return true;
    }
    
    return false;
}

/*!
    \brief      初始化配置系统
    \param[in]  none
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int config_system_init(void)
{
    my_printf(DEBUG_USART, "初始化配置系统...\r\n");
    
    // 检查配置文件是否存在
    if (!config_file_exists("config.ini")) {
        my_printf(DEBUG_USART, "配置文件不存在，创建默认配置文件\r\n");
        config_create_sample_file();
    }
    
    // 加载配置
    if (load_application_config() == 0) {
        my_printf(DEBUG_USART, "配置系统初始化成功\r\n");
        return 0;
    } else {
        my_printf(DEBUG_USART, "配置系统初始化失败\r\n");
        return -1;
    }
}

/*
 * 使用说明：
 * 
 * 1. 在main函数中调用 config_system_init() 初始化配置系统
 * 2. 使用 get_app_config() 获取配置结构体
 * 3. 使用 config_get_xxx() 函数动态读取配置
 * 4. 使用 config_complete_test() 进行完整测试
 * 
 * 配置文件格式示例：
 * [system]
 * device_name = CIMC-Device
 * version = 1.0.0
 * debug_mode = true
 * max_users = 100
 * 
 * [network]
 * ip_address = ***********00
 * port = 8080
 * timeout = 30.5
 * enable_dhcp = false
 */
