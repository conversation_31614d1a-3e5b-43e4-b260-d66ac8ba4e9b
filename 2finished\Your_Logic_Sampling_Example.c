/*
 * 按照宝宝的逻辑实现采样功能
 * 电压: ADC的0-3.3V × ratio
 * 时间: RTC now的时间格式
 * 周期: country变量
 * 逻辑: 收到start时，每经过一个country周期更新并串口打印
 */

#include "mcu_cmic_gd32f470vet6.h"

// 外部变量声明（来自您的代码）
extern uint8_t country;        // 您的周期变量（来自task_key.c）
extern uint8_t uart_flag;      // 您的采样标志
extern float current_ratio;    // 当前变比

// 采样相关变量
static uint32_t sample_timer = 0;    // 采样计时器（秒）
static uint8_t sampling_active = 0;  // 采样状态

/*!
    \brief      处理start命令 - 按照您的逻辑
    \param[in]  none
    \param[out] none
    \retval     none
*/
void process_start_command(void)
{
    // 启动采样
    sampling_active = 1;
    uart_flag = 1;
    sample_timer = 0;  // 重置计时器
    
    // 输出启动信息
    my_printf(DEBUG_USART, "Periodic Sampling\r\n");
    my_printf(DEBUG_USART, "sample cycle: %ds\r\n", country);
}

/*!
    \brief      处理stop命令 - 按照您的逻辑
    \param[in]  none
    \param[out] none
    \retval     none
*/
void process_stop_command(void)
{
    // 停止采样
    sampling_active = 0;
    uart_flag = 0;
    sample_timer = 0;
    
    // 输出停止信息
    my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");
    
    // 更新OLED显示
    OLED_ShowStr(0, 0, "system idle", 8);
    OLED_ShowStr(0, 2, "           ", 8);  // 清空第二行
}

/*!
    \brief      获取RTC now格式的时间字符串
    \param[in]  time_str: 输出缓冲区
    \param[in]  size: 缓冲区大小
    \param[out] none
    \retval     none
*/
void get_rtc_now_time_string(char* time_str, size_t size)
{
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);
    
    // 转换BCD到十进制
    uint8_t year = bcd_to_decimal(rtc_time.year);
    uint8_t month = rtc_enum_to_month(rtc_time.month);
    uint8_t day = bcd_to_decimal(rtc_time.date);
    uint8_t hour = bcd_to_decimal(rtc_time.hour);
    uint8_t minute = bcd_to_decimal(rtc_time.minute);
    uint8_t second = bcd_to_decimal(rtc_time.second);
    
    // 格式化为RTC now的格式：YYYY-MM-DD HH:MM:SS
    snprintf(time_str, size, "20%02d-%02d-%02d %02d:%02d:%02d",
             year, month, day, hour, minute, second);
}

/*!
    \brief      获取ADC电压并乘以ratio
    \param[in]  none
    \param[out] none
    \retval     float: 计算后的电压值
*/
float get_adc_voltage_with_ratio(void)
{
    // 获取ADC的0-3.3V电压
    float adc_voltage = get_channel_voltage(0);  // 您的ADC函数
    
    // 乘以ratio得到实际电压
    float actual_voltage = adc_voltage * current_ratio;
    
    return actual_voltage;
}

/*!
    \brief      采样任务 - 按照您的逻辑实现
    \param[in]  none
    \param[out] none
    \retval     none
    \note       这个函数需要在主循环中每秒调用一次
*/
void your_sampling_task(void)
{
    // 如果没有启动采样，直接返回
    if (!sampling_active || !uart_flag) {
        return;
    }
    
    // 计时器递增（假设每秒调用一次）
    sample_timer++;
    
    // 检查是否到了采样时间（每经过一个country周期）
    if (sample_timer >= country) {
        // 重置计时器
        sample_timer = 0;
        
        // 获取RTC now格式的时间
        char time_str[32];
        get_rtc_now_time_string(time_str, sizeof(time_str));
        
        // 获取ADC电压×ratio
        float voltage = get_adc_voltage_with_ratio();
        
        // 检查是否超限
        if (voltage > current_limit) {
            // 超限：点亮LED2，输出OverLimit信息
            LED2_SET(1);
            my_printf(DEBUG_USART, "%s ch0=%.1fV OverLimit (%.2f) !\r\n",
                      time_str, voltage, current_limit);
        } else {
            // 正常：熄灭LED2，正常输出
            LED2_SET(0);
            my_printf(DEBUG_USART, "%s ch0=%.1fV\r\n", time_str, voltage);
        }
        
        // 更新OLED显示
        update_oled_display_with_current_data();
    }
}

/*!
    \brief      更新OLED显示当前数据
    \param[in]  none
    \param[out] none
    \retval     none
*/
void update_oled_display_with_current_data(void)
{
    if (sampling_active && uart_flag) {
        // 采样状态：显示时间和电压
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        
        // 转换BCD到十进制
        uint8_t hour = bcd_to_decimal(rtc_time.hour);
        uint8_t minute = bcd_to_decimal(rtc_time.minute);
        uint8_t second = bcd_to_decimal(rtc_time.second);
        
        // 显示时间 (hh:mm:ss)
        char time_str[16];
        snprintf(time_str, sizeof(time_str), "%02d:%02d:%02d", hour, minute, second);
        OLED_ShowStr(0, 0, time_str, 8);
        
        // 显示电压 (xx.xx V)
        float voltage = get_adc_voltage_with_ratio();
        char voltage_str[16];
        snprintf(voltage_str, sizeof(voltage_str), "%.2f V", voltage);
        OLED_ShowStr(0, 2, voltage_str, 8);
    } else {
        // 停止状态：显示system idle
        OLED_ShowStr(0, 0, "system idle", 8);
        OLED_ShowStr(0, 2, "           ", 8);  // 清空第二行
    }
}

/*!
    \brief      按键周期调整 - 使用您的country变量
    \param[in]  none
    \param[out] none
    \retval     none
*/
void your_btn_task(void)
{
    uint8_t key_val = key_read();
    uint8_t key_down = key_val & (key_old ^ key_val);
    key_old = key_val;
    
    // KEY2/KEY3/KEY4 周期调整（您的逻辑）
    if (key_down > 1 && key_down <= 4) {
        country = (key_down - 1) * 5;  // KEY2=5s, KEY3=10s, KEY4=15s
        
        // 输出周期调整信息
        my_printf(DEBUG_USART, "sample cycle adjust: %ds\r\n", country);
        
        // 保存到Flash（可选）
        save_sample_cycle_to_flash(country);
    }
    
    // KEY1 启停控制（您的逻辑）
    if (key_down == 1) {
        LED1_TOGGLE;
        trager_flag ^= 1;
        uart_flag ^= 1;  // 切换采样状态
        
        if (uart_flag) {
            // 启动采样
            process_start_command();
        } else {
            // 停止采样
            process_stop_command();
        }
    }
}

/*!
    \brief      串口命令处理 - 添加start/stop命令
    \param[in]  command: 命令字符串
    \param[out] none
    \retval     none
*/
void process_your_uart_command(char* command)
{
    // ... 其他命令处理 ...
    
    if (strcmp(command, "start") == 0) {
        // 启动周期采样
        process_start_command();
    } else if (strcmp(command, "stop") == 0) {
        // 停止周期采样
        process_stop_command();
    }
    
    // ... 其他命令处理 ...
}

/*!
    \brief      使用示例
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usage_example(void)
{
    printf("\r\n=== 按照宝宝逻辑的采样系统使用示例 ===\r\n");
    
    printf("1. 初始状态:\r\n");
    printf("   country = 5 (默认5秒周期)\r\n");
    printf("   uart_flag = 0 (未启动)\r\n");
    printf("   OLED显示: system idle\r\n");
    
    printf("\r\n2. 发送start命令:\r\n");
    printf("   输出: Periodic Sampling\r\n");
    printf("   输出: sample cycle: 5s\r\n");
    printf("   uart_flag = 1\r\n");
    printf("   开始计时\r\n");
    
    printf("\r\n3. 每5秒输出一次:\r\n");
    printf("   获取RTC now时间: 2025-01-01 00:30:05\r\n");
    printf("   获取ADC电压×ratio: 10.5V\r\n");
    printf("   输出: 2025-01-01 00:30:05 ch0=10.5V\r\n");
    printf("   更新OLED显示\r\n");
    
    printf("\r\n4. 按KEY3调整周期:\r\n");
    printf("   country = 10\r\n");
    printf("   输出: sample cycle adjust: 10s\r\n");
    printf("   现在每10秒输出一次\r\n");
    
    printf("\r\n5. 发送stop命令:\r\n");
    printf("   输出: Periodic Sampling STOP\r\n");
    printf("   uart_flag = 0\r\n");
    printf("   OLED显示: system idle\r\n");
    
    printf("\r\n=== 完全按照您的逻辑实现！===\r\n");
}

/*
主循环中的调用示例：

void main_loop(void)
{
    while(1) {
        uart_task();           // 处理串口命令
        your_btn_task();       // 处理按键（使用country变量）
        your_sampling_task();  // 采样任务（每秒调用）
        led_task();           // LED任务
        
        delay_1s();           // 1秒延时
    }
}
*/
