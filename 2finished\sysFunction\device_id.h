/*
 * device_id.h
 * 功能：设备ID管理模块，提供设备ID的存储、读取和管理功能
 * 作者：按照宝宝的思路实现
 * 版权：Copyright (c) 2024. All rights reserved.
 */

#ifndef DEVICE_ID_H
#define DEVICE_ID_H

#include <stdint.h>

// 设备ID相关常量定义
#define DEVICE_ID_MAX_LENGTH    64              // 设备ID最大长度
#define DEVICE_ID_FLASH_ADDR    0x2000          // 设备ID在Flash中的存储地址

// 设备ID状态枚举
typedef enum {
    DEVICE_ID_OK = 0,           // 操作成功
    DEVICE_ID_ERROR = -1,       // 一般错误
    DEVICE_ID_INVALID = -2,     // 参数无效
    DEVICE_ID_NOT_FOUND = -3,   // 设备ID未找到
    DEVICE_ID_FLASH_ERROR = -4  // Flash操作错误
} device_id_status_t;

// 函数声明

/*!
    \brief      初始化设备ID模块
    \param[in]  none
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_init(void);

/*!
    \brief      从Flash读取设备ID
    \param[in]  device_id: 设备ID缓冲区指针
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_read(char *device_id);

/*!
    \brief      写入设备ID到Flash
    \param[in]  device_id: 要写入的设备ID字符串
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_write(const char *device_id);

/*!
    \brief      设置默认设备ID
    \param[in]  none
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_set_default(void);

/*!
    \brief      获取当前设备ID字符串
    \param[in]  none
    \param[out] none
    \retval     const char*: 设备ID字符串指针
*/
const char* device_id_get(void);

/*!
    \brief      打印系统启动信息（包含设备ID）
    \param[in]  none
    \param[out] none
    \retval     none
*/
void device_id_print_startup_info(void);

#endif // DEVICE_ID_H
