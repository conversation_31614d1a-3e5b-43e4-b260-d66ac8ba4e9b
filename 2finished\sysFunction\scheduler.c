/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"

// Global variable to store task count
uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

// 外部函数声明
extern void software_rtc_task(void);

// Static task array, each task contains: function pointer, execution period (ms), last run time (ms)
static task_t scheduler_task[] =
{
			{led_task,  1000,   0}, // LED1闪烁周期调整为1000ms(1秒)
     {key_task,  1,     0},
     {adc_task,  100,   0},
     {oled_task, 10,    0},
     {uart_task, 5,     0},
     {sampling_task, 1000, 0}, // 采样任务，1000ms周期
     {software_rtc_task, 100, 0}, // 软件RTC任务，100ms周期

};

/**
 * @brief Scheduler initialization function
 * Calculate the number of task array elements and store in task_num
 */
void scheduler_init(void)
{
    // Calculate the number of task array elements and store in task_num
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief Scheduler run function
 * Traverse the task array, check if each task needs to be executed. If current time has reached the task execution period, execute the task and update the last run time
 */
void scheduler_run(void)
{
    // Traverse all tasks in the task array
    for (uint8_t i = 0; i < task_num; i++)
    {
        // Get current system time (milliseconds)
        uint32_t now_time = get_system_ms();

        // Check if current time has reached the task execution time
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // Update the task's last run time to current time
            scheduler_task[i].last_run = now_time;

            // Execute the task
            scheduler_task[i].task_func();
        }
    }
}


