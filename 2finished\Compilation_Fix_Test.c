/*
 * 编译修复验证程序
 * 验证LED和OLED函数调用是否正确
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      测试LED控制函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_led_control(void)
{
    printf("\r\n=== LED控制测试 ===\r\n");
    
    printf("1. 测试LED1_SET(0) - 常灭：\r\n");
    LED1_SET(0);
    printf("   LED1应该熄灭\r\n");
    
    printf("\r\n2. 测试LED1_SET(1) - 常亮：\r\n");
    LED1_SET(1);
    printf("   LED1应该点亮\r\n");
    
    printf("\r\n3. 测试LED1_TOGGLE - 切换：\r\n");
    LED1_TOGGLE;
    printf("   LED1状态应该切换\r\n");
    
    printf("\r\n=== LED控制测试完成 ===\r\n");
}

/*!
    \brief      测试OLED显示函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_oled_display(void)
{
    printf("\r\n=== OLED显示测试 ===\r\n");
    
    printf("1. 测试OLED_ShowStr函数：\r\n");
    
    // 测试显示"system idle"
    printf("   显示 'system idle' 在第一行\r\n");
    OLED_ShowStr(0, 0, "system idle", 8);
    
    // 测试清空第二行
    printf("   清空第二行\r\n");
    OLED_ShowStr(0, 2, "           ", 8);
    
    // 测试显示时间
    printf("   显示时间格式 '12:34:56'\r\n");
    OLED_ShowStr(0, 0, "12:34:56", 8);
    
    // 测试显示电压
    printf("   显示电压格式 '1.65 V'\r\n");
    OLED_ShowStr(0, 2, "1.65 V", 8);
    
    printf("\r\n=== OLED显示测试完成 ===\r\n");
}

/*!
    \brief      测试采样控制函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_sampling_functions(void)
{
    printf("\r\n=== 采样控制函数测试 ===\r\n");
    
    printf("1. 测试电压采样函数：\r\n");
    for (int i = 0; i < 5; i++) {
        float voltage = get_channel_voltage(0);
        printf("   采样%d: %.2fV", i+1, voltage);
        if (voltage >= 0.0f && voltage <= 3.3f) {
            printf(" ✓ 范围正确\r\n");
        } else {
            printf(" ✗ 超出0-3.3V范围\r\n");
        }
    }
    
    printf("\r\n2. 测试启动采样：\r\n");
    start_periodic_sampling();
    
    printf("\r\n3. 测试OLED更新：\r\n");
    update_oled_display();
    
    printf("\r\n4. 测试停止采样：\r\n");
    stop_periodic_sampling();
    
    printf("\r\n=== 采样控制函数测试完成 ===\r\n");
}

/*!
    \brief      测试按键功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_key_functions(void)
{
    printf("\r\n=== 按键功能测试 ===\r\n");
    
    printf("1. 测试启停切换：\r\n");
    toggle_sampling_state();
    printf("   采样状态已切换\r\n");
    
    printf("\r\n2. 测试周期设置：\r\n");
    printf("   设置5s周期：\r\n");
    set_sample_cycle(5);
    
    printf("   设置10s周期：\r\n");
    set_sample_cycle(10);
    
    printf("   设置15s周期：\r\n");
    set_sample_cycle(15);
    
    printf("\r\n=== 按键功能测试完成 ===\r\n");
}

/*!
    \brief      测试LED闪烁任务
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_led_blink_task(void)
{
    printf("\r\n=== LED闪烁任务测试 ===\r\n");
    
    printf("1. 启动采样（LED应该开始闪烁）：\r\n");
    start_periodic_sampling();
    
    printf("2. 模拟LED闪烁任务调用：\r\n");
    for (int i = 0; i < 5; i++) {
        uint32_t sim_time = i * 600;  // 模拟600ms间隔
        led_blink_task(sim_time);
        printf("   第%d次调用 led_blink_task(%d)\r\n", i+1, sim_time);
    }
    
    printf("\r\n3. 停止采样（LED应该常灭）：\r\n");
    stop_periodic_sampling();
    
    printf("\r\n=== LED闪烁任务测试完成 ===\r\n");
}

/*!
    \brief      完整的编译修复验证
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_compilation_fix_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        编译修复验证程序              #\r\n");
    printf("##########################################\r\n");
    
    printf("修复的问题：\r\n");
    printf("1. LED1_OFF → LED1_SET(0)\r\n");
    printf("2. OLED_ShowString → OLED_ShowStr\r\n");
    printf("3. 调整OLED显示参数\r\n");
    printf("\r\n");
    
    // 1. LED控制测试
    test_led_control();
    
    // 2. OLED显示测试
    test_oled_display();
    
    // 3. 采样控制函数测试
    test_sampling_functions();
    
    // 4. 按键功能测试
    test_key_functions();
    
    // 5. LED闪烁任务测试
    test_led_blink_task();
    
    printf("\r\n##########################################\r\n");
    printf("#        验证完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n✅ 编译错误已修复：\r\n");
    printf("1. LED控制：使用 LED1_SET(0/1) 和 LED1_TOGGLE\r\n");
    printf("2. OLED显示：使用 OLED_ShowStr 函数\r\n");
    printf("3. 显示位置：第一行y=0，第二行y=2\r\n");
    printf("4. 字体大小：使用8号字体\r\n");
    
    printf("\r\n🎯 现在可以正常编译和运行！\r\n");
    
    printf("\r\n📋 使用说明：\r\n");
    printf("1. 编译项目（应该0错误0警告）\r\n");
    printf("2. 烧录到目标板\r\n");
    printf("3. 通过串口发送命令测试：\r\n");
    printf("   - start: 启动采样\r\n");
    printf("   - stop:  停止采样\r\n");
    printf("4. 按键测试：\r\n");
    printf("   - KEY1: 启停控制\r\n");
    printf("   - KEY2: 5s周期\r\n");
    printf("   - KEY3: 10s周期\r\n");
    printf("   - KEY4: 15s周期\r\n");
    printf("5. 观察LED1闪烁和OLED显示\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用：
 * complete_compilation_fix_test();
 * 
 * 这会验证所有修复的函数调用：
 * 1. LED控制函数
 * 2. OLED显示函数
 * 3. 采样控制函数
 * 4. 按键功能
 * 5. LED闪烁任务
 * 
 * 修复的问题：
 * - LED1_OFF → LED1_SET(0)
 * - OLED_ShowString → OLED_ShowStr
 * - 调整了OLED显示参数
 * 
 * 现在应该可以正常编译和运行！
 */
