/*
 * config.ini文件快速测试程序
 * 用于快速验证配置文件读取功能
 */

#include "mcu_cmic_gd32f470vet6.h"

// 如果你已经包含了config_reader.h，取消下面的注释
// #include "config_reader.h"

/*!
    \brief      快速测试配置文件功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void quick_test_config_file(void)
{
    printf("\r\n=== 配置文件快速测试 ===\r\n");
    
    // 测试文件系统是否正常
    printf("1. 测试文件系统...\r\n");
    sd_fatfs_test();
    
    // 如果config_reader已集成，取消下面的注释
    /*
    printf("2. 测试配置文件读取...\r\n");
    
    // 创建示例配置文件
    config_create_sample_file();
    
    // 加载配置文件
    if (config_load_from_file("config.ini") == 0) {
        printf("配置文件加载成功！\r\n");
        
        // 读取一些配置项
        const char* device_name = config_get_string("system", "device_name", "Unknown");
        int port = config_get_int("network", "port", 0);
        float timeout = config_get_float("network", "timeout", 0.0f);
        bool debug = config_get_bool("system", "debug_mode", false);
        
        printf("设备名称: %s\r\n", device_name);
        printf("网络端口: %d\r\n", port);
        printf("超时时间: %.1f\r\n", timeout);
        printf("调试模式: %s\r\n", debug ? "开启" : "关闭");
        
        // 打印所有配置项
        config_print_all();
    } else {
        printf("配置文件加载失败！\r\n");
    }
    */
    
    printf("=== 测试完成 ===\r\n");
}

/*!
    \brief      手动创建配置文件测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void manual_create_config_test(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    printf("\r\n=== 手动创建配置文件测试 ===\r\n");
    
    const char* test_config = 
        "[system]\r\n"
        "device_name=Test-Device\r\n"
        "version=1.0.0\r\n"
        "debug=true\r\n"
        "\r\n"
        "[network]\r\n"
        "ip=*************\r\n"
        "port=8080\r\n";
    
    // 创建配置文件
    result = f_open(&config_file, "0:/test_config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        result = f_write(&config_file, test_config, strlen(test_config), &bytes_written);
        f_close(&config_file);
        
        if (result == FR_OK) {
            printf("测试配置文件创建成功: test_config.ini\r\n");
            printf("写入字节数: %d\r\n", bytes_written);
        } else {
            printf("写入配置文件失败，错误码: %d\r\n", result);
        }
    } else {
        printf("创建配置文件失败，错误码: %d\r\n", result);
    }
    
    // 尝试读取刚创建的文件
    char read_buffer[256];
    UINT bytes_read;
    
    result = f_open(&config_file, "0:/test_config.ini", FA_READ);
    if (result == FR_OK) {
        result = f_read(&config_file, read_buffer, sizeof(read_buffer) - 1, &bytes_read);
        f_close(&config_file);
        
        if (result == FR_OK) {
            read_buffer[bytes_read] = '\0';
            printf("读取配置文件成功:\r\n%s\r\n", read_buffer);
        } else {
            printf("读取配置文件失败，错误码: %d\r\n", result);
        }
    } else {
        printf("打开配置文件失败，错误码: %d\r\n", result);
    }
    
    printf("=== 手动测试完成 ===\r\n");
}

/*!
    \brief      检查SD卡和文件系统状态
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_filesystem_status(void)
{
    printf("\r\n=== 文件系统状态检查 ===\r\n");
    
    // 检查SD卡初始化状态
    DSTATUS disk_status = disk_initialize(0);
    printf("SD卡初始化状态: %d\r\n", disk_status);
    
    if (disk_status == 0) {
        printf("SD卡初始化成功\r\n");
        
        // 尝试挂载文件系统
        extern FATFS fs;
        FRESULT mount_result = f_mount(0, &fs);
        printf("文件系统挂载结果: %d\r\n", mount_result);
        
        if (mount_result == FR_OK) {
            printf("文件系统挂载成功\r\n");
            
            // 获取磁盘信息
            DWORD free_clusters;
            FATFS* fs_ptr;
            FRESULT free_result = f_getfree("0:", &free_clusters, &fs_ptr);
            
            if (free_result == FR_OK) {
                DWORD total_sectors = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
                DWORD free_sectors = free_clusters * fs_ptr->csize;
                
                printf("总扇区数: %lu\r\n", total_sectors);
                printf("空闲扇区数: %lu\r\n", free_sectors);
                printf("扇区大小: %d 字节\r\n", 512);
                printf("总容量: %lu KB\r\n", total_sectors / 2);
                printf("空闲容量: %lu KB\r\n", free_sectors / 2);
            }
        } else {
            printf("文件系统挂载失败\r\n");
        }
    } else {
        printf("SD卡初始化失败\r\n");
    }
    
    printf("=== 状态检查完成 ===\r\n");
}

/*!
    \brief      完整的配置文件问题诊断
    \param[in]  none
    \param[out] none
    \retval     none
*/
void diagnose_config_file_problem(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        配置文件问题诊断              #\r\n");
    printf("##########################################\r\n");
    
    // 1. 检查文件系统状态
    check_filesystem_status();
    
    // 2. 手动创建和读取测试
    manual_create_config_test();
    
    // 3. 快速功能测试
    quick_test_config_file();
    
    printf("\r\n##########################################\r\n");
    printf("#        诊断完成                      #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n解决步骤：\r\n");
    printf("1. 确保SD卡正确插入\r\n");
    printf("2. 检查SD卡格式（建议FAT32）\r\n");
    printf("3. 确认文件系统初始化成功\r\n");
    printf("4. 集成config_reader模块\r\n");
    printf("5. 在SD卡根目录放置config.ini文件\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行诊断：
 * 
 * 1. diagnose_config_file_problem();  // 完整诊断
 * 2. check_filesystem_status();       // 检查文件系统
 * 3. manual_create_config_test();     // 手动测试
 * 4. quick_test_config_file();        // 快速测试
 * 
 * 根据输出结果确定问题所在，然后：
 * - 如果SD卡有问题，检查硬件连接
 * - 如果文件系统有问题，重新格式化SD卡
 * - 如果读取有问题，集成config_reader模块
 */
