/*
 * config.ini文件读取功能测试程序
 * 验证是否完全符合题目要求
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      创建标准格式的config.ini文件
    \param[in]  none
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int create_standard_config_ini(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        result = f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        if (result == FR_OK) {
            my_printf(DEBUG_USART, "标准config.ini文件创建成功\r\n");
            my_printf(DEBUG_USART, "文件内容:\r\n%s\r\n", config_content);
            return 0;
        }
    }
    
    my_printf(DEBUG_USART, "创建config.ini文件失败\r\n");
    return -1;
}

/*!
    \brief      创建不同格式的测试配置文件
    \param[in]  test_case: 测试用例编号
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int create_test_config_ini(int test_case)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    const char* config_content;
    
    switch (test_case) {
        case 1:
            // 标准格式
            config_content = 
                "[Ratio]\r\n"
                "Ch0 = 2.50\r\n"
                "\r\n"
                "[Limit]\r\n"
                "Ch0 = 15.75\r\n";
            break;
            
        case 2:
            // 带空格格式
            config_content = 
                "[Ratio]\r\n"
                "Ch0  =  3.14  \r\n"
                "\r\n"
                "[Limit]\r\n"
                "Ch0  =  20.00  \r\n";
            break;
            
        case 3:
            // 带注释格式
            config_content = 
                "; 这是配置文件\r\n"
                "[Ratio]\r\n"
                "Ch0 = 1.23\r\n"
                "\r\n"
                "[Limit]\r\n"
                "Ch0 = 5.67\r\n";
            break;
            
        default:
            return -1;
    }
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        result = f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        if (result == FR_OK) {
            my_printf(DEBUG_USART, "测试配置文件%d创建成功\r\n", test_case);
            return 0;
        }
    }
    
    return -1;
}

/*!
    \brief      删除config.ini文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void delete_config_ini(void)
{
    f_unlink("0:/config.ini");
    my_printf(DEBUG_USART, "config.ini文件已删除\r\n");
}

/*!
    \brief      测试conf命令 - 文件不存在的情况
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_conf_file_not_found(void)
{
    my_printf(DEBUG_USART, "\r\n=== 测试：文件不存在 ===\r\n");
    
    // 确保文件不存在
    delete_config_ini();
    
    // 模拟发送conf命令
    my_printf(DEBUG_USART, "模拟输入: conf\r\n");
    process_uart_command("conf");
    
    my_printf(DEBUG_USART, "期望输出: config.ini file not found.\r\n");
    my_printf(DEBUG_USART, "========================\r\n");
}

/*!
    \brief      测试conf命令 - 文件存在的情况
    \param[in]  test_case: 测试用例编号
    \param[out] none
    \retval     none
*/
void test_conf_file_exists(int test_case)
{
    my_printf(DEBUG_USART, "\r\n=== 测试：文件存在 (用例%d) ===\r\n", test_case);
    
    // 创建测试配置文件
    if (create_test_config_ini(test_case) == 0) {
        // 模拟发送conf命令
        my_printf(DEBUG_USART, "模拟输入: conf\r\n");
        process_uart_command("conf");
        
        my_printf(DEBUG_USART, "期望输出格式:\r\n");
        my_printf(DEBUG_USART, "Ratio = x.xx\r\n");
        my_printf(DEBUG_USART, "Limit = x.xx\r\n");
        my_printf(DEBUG_USART, "config read success\r\n");
    } else {
        my_printf(DEBUG_USART, "创建测试文件失败\r\n");
    }
    
    my_printf(DEBUG_USART, "========================\r\n");
}

/*!
    \brief      验证读取的配置值
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_config_values(void)
{
    my_printf(DEBUG_USART, "\r\n=== 验证配置值 ===\r\n");
    
    float ratio = get_config_ratio();
    float limit = get_config_limit();
    
    my_printf(DEBUG_USART, "当前配置值:\r\n");
    my_printf(DEBUG_USART, "Ratio = %.2f\r\n", ratio);
    my_printf(DEBUG_USART, "Limit = %.2f\r\n", limit);
    
    my_printf(DEBUG_USART, "================\r\n");
}

/*!
    \brief      完整的配置文件读取测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_ini_complete_test(void)
{
    my_printf(DEBUG_USART, "\r\n##########################################\r\n");
    my_printf(DEBUG_USART, "#        config.ini读取功能测试        #\r\n");
    my_printf(DEBUG_USART, "##########################################\r\n");
    
    // 测试1：文件不存在
    test_conf_file_not_found();
    
    // 测试2：标准格式文件
    test_conf_file_exists(1);
    verify_config_values();
    
    // 测试3：带空格格式文件
    test_conf_file_exists(2);
    verify_config_values();
    
    // 测试4：带注释格式文件
    test_conf_file_exists(3);
    verify_config_values();
    
    // 测试5：创建题目要求的标准文件
    my_printf(DEBUG_USART, "\r\n=== 创建题目标准格式文件 ===\r\n");
    if (create_standard_config_ini() == 0) {
        my_printf(DEBUG_USART, "模拟输入: conf\r\n");
        process_uart_command("conf");
        verify_config_values();
    }
    
    my_printf(DEBUG_USART, "\r\n##########################################\r\n");
    my_printf(DEBUG_USART, "#        测试完成                      #\r\n");
    my_printf(DEBUG_USART, "##########################################\r\n");
    
    my_printf(DEBUG_USART, "\r\n现在可以手动测试：\r\n");
    my_printf(DEBUG_USART, "1. 发送 'conf' 命令测试文件读取\r\n");
    my_printf(DEBUG_USART, "2. 删除config.ini后再次发送 'conf' 测试文件不存在\r\n");
    my_printf(DEBUG_USART, "3. 创建自定义config.ini文件测试\r\n");
}

/*!
    \brief      手动创建题目要求格式的配置文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void create_requirement_config_file(void)
{
    my_printf(DEBUG_USART, "\r\n=== 创建题目要求格式的配置文件 ===\r\n");
    
    if (create_standard_config_ini() == 0) {
        my_printf(DEBUG_USART, "已创建符合题目要求的config.ini文件\r\n");
        my_printf(DEBUG_USART, "文件格式:\r\n");
        my_printf(DEBUG_USART, "[Ratio]\r\n");
        my_printf(DEBUG_USART, "Ch0 = 1.99\r\n");
        my_printf(DEBUG_USART, "\r\n");
        my_printf(DEBUG_USART, "[Limit]\r\n");
        my_printf(DEBUG_USART, "Ch0 = 10.11\r\n");
        my_printf(DEBUG_USART, "\r\n");
        my_printf(DEBUG_USART, "现在可以发送 'conf' 命令测试读取功能\r\n");
    } else {
        my_printf(DEBUG_USART, "创建配置文件失败\r\n");
    }
    
    my_printf(DEBUG_USART, "===============================\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行测试：
 * 
 * 1. config_ini_complete_test();        // 完整测试
 * 2. create_requirement_config_file();  // 创建标准格式文件
 * 3. test_conf_file_not_found();        // 测试文件不存在
 * 4. test_conf_file_exists(1);          // 测试文件存在
 * 
 * 然后通过串口发送以下命令进行实际测试：
 * - "conf" -> 根据文件是否存在返回相应结果
 * 
 * 期望的交互：
 * 输入: conf
 * 输出: config.ini file not found.  (文件不存在时)
 * 
 * 输入: conf
 * 输出: Ratio = 1.99             (文件存在时)
 *       Limit = 10.11
 *       config read success
 */
