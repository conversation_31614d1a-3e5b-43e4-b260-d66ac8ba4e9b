/*
 * config.ini逻辑测试程序
 * 验证所有逻辑是否正确，完全符合题目要求
 */

#include "mcu_cmic_gd32f470vet6.h"

/*!
    \brief      创建标准格式的config.ini文件用于测试
    \param[in]  none
    \param[out] none
    \retval     0: 成功, -1: 失败
*/
int create_test_config_ini(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    // 完全按照题目要求的格式
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    printf("创建测试配置文件...\r\n");
    printf("文件内容:\r\n%s\r\n", config_content);
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        result = f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        if (result == FR_OK) {
            printf("配置文件创建成功，写入 %d 字节\r\n", bytes_written);
            return 0;
        }
    }
    
    printf("配置文件创建失败，错误码: %d\r\n", result);
    return -1;
}

/*!
    \brief      删除config.ini文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void delete_config_ini(void)
{
    FRESULT result = f_unlink("0:/config.ini");
    printf("删除config.ini文件，结果: %d\r\n", result);
}

/*!
    \brief      测试文件不存在的情况
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_file_not_found(void)
{
    printf("\r\n=== 测试：文件不存在 ===\r\n");
    
    // 确保文件不存在
    delete_config_ini();
    
    printf("期望输出: config. ini file not found.\r\n");
    printf("实际输出: ");
    
    // 调用conf命令
    process_uart_command("conf");
    
    printf("========================\r\n");
}

/*!
    \brief      测试文件存在的情况
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_file_exists(void)
{
    printf("\r\n=== 测试：文件存在 ===\r\n");
    
    // 创建标准配置文件
    if (create_test_config_ini() == 0) {
        printf("期望输出:\r\n");
        printf("Ratio = 1.99\r\n");
        printf("Limit= 10.11\r\n");
        printf("config read success\r\n");
        printf("\r\n实际输出:\r\n");
        
        // 调用conf命令
        process_uart_command("conf");
        
        // 验证读取的值
        printf("\r\n验证读取的配置值:\r\n");
        printf("config_ratio = %.2f (期望: 1.99)\r\n", get_config_ratio());
        printf("config_limit = %.2f (期望: 10.11)\r\n", get_config_limit());
        
        // 检查值是否正确
        if (get_config_ratio() == 1.99f && get_config_limit() == 10.11f) {
            printf("✓ 配置值读取正确\r\n");
        } else {
            printf("✗ 配置值读取错误\r\n");
        }
    } else {
        printf("创建测试文件失败\r\n");
    }
    
    printf("========================\r\n");
}

/*!
    \brief      测试不同数值的配置文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_different_values(void)
{
    printf("\r\n=== 测试：不同数值 ===\r\n");
    
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    // 测试不同的数值
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0 = 2.50\r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0 = 15.75\r\n";
    
    printf("创建不同数值的配置文件:\r\n%s\r\n", config_content);
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        printf("期望输出:\r\n");
        printf("Ratio = 2.50\r\n");
        printf("Limit= 15.75\r\n");
        printf("config read success\r\n");
        printf("\r\n实际输出:\r\n");
        
        // 调用conf命令
        process_uart_command("conf");
        
        // 验证读取的值
        printf("\r\n验证读取的配置值:\r\n");
        printf("config_ratio = %.2f (期望: 2.50)\r\n", get_config_ratio());
        printf("config_limit = %.2f (期望: 15.75)\r\n", get_config_limit());
    }
    
    printf("========================\r\n");
}

/*!
    \brief      测试带空格的配置文件
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_with_spaces(void)
{
    printf("\r\n=== 测试：带空格格式 ===\r\n");
    
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    
    // 测试带空格的格式
    const char* config_content = 
        "[Ratio]\r\n"
        "Ch0  =  3.14  \r\n"
        "\r\n"
        "[Limit]\r\n"
        "Ch0  =  20.00  \r\n";
    
    printf("创建带空格的配置文件:\r\n%s\r\n", config_content);
    
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        f_write(&config_file, config_content, strlen(config_content), &bytes_written);
        f_close(&config_file);
        
        printf("实际输出:\r\n");
        
        // 调用conf命令
        process_uart_command("conf");
        
        // 验证读取的值
        printf("\r\n验证读取的配置值:\r\n");
        printf("config_ratio = %.2f (期望: 3.14)\r\n", get_config_ratio());
        printf("config_limit = %.2f (期望: 20.00)\r\n", get_config_limit());
    }
    
    printf("========================\r\n");
}

/*!
    \brief      检查文件系统状态
    \param[in]  none
    \param[out] none
    \retval     none
*/
void check_filesystem_status(void)
{
    printf("\r\n=== 文件系统状态检查 ===\r\n");
    
    // 检查SD卡初始化状态
    DSTATUS disk_status = disk_initialize(0);
    printf("SD卡初始化状态: %d (0=成功)\r\n", disk_status);
    
    if (disk_status == 0) {
        // 尝试挂载文件系统
        extern FATFS fs;
        FRESULT mount_result = f_mount(0, &fs);
        printf("文件系统挂载结果: %d (0=成功)\r\n", mount_result);
        
        if (mount_result == FR_OK) {
            printf("✓ 文件系统正常\r\n");
        } else {
            printf("✗ 文件系统挂载失败\r\n");
        }
    } else {
        printf("✗ SD卡初始化失败\r\n");
    }
    
    printf("========================\r\n");
}

/*!
    \brief      完整的逻辑测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void complete_logic_test(void)
{
    printf("\r\n##########################################\r\n");
    printf("#        config.ini逻辑完整测试        #\r\n");
    printf("##########################################\r\n");
    
    // 1. 检查文件系统状态
    check_filesystem_status();
    
    // 2. 测试文件不存在的情况
    test_file_not_found();
    
    // 3. 测试文件存在的情况（标准格式）
    test_file_exists();
    
    // 4. 测试不同数值
    test_different_values();
    
    // 5. 测试带空格格式
    test_with_spaces();
    
    printf("\r\n##########################################\r\n");
    printf("#        逻辑测试完成                  #\r\n");
    printf("##########################################\r\n");
    
    printf("\r\n总结：\r\n");
    printf("1. 文件不存在时输出: config. ini file not found.\r\n");
    printf("2. 文件存在时输出: Ratio = x.xx, Limit= x.xx, config read success\r\n");
    printf("3. 支持不同数值和格式的配置文件\r\n");
    printf("4. 配置值正确保存到内存变量中\r\n");
    printf("\r\n现在可以通过串口手动发送 'conf' 命令进行测试\r\n");
}

/*!
    \brief      验证输出格式是否完全符合题目要求
    \param[in]  none
    \param[out] none
    \retval     none
*/
void verify_exact_format(void)
{
    printf("\r\n=== 验证输出格式 ===\r\n");
    
    printf("题目要求的精确格式：\r\n");
    printf("文件不存在时:\r\n");
    printf("  输入: conf\r\n");
    printf("  输出: config. ini file not found.\r\n");
    printf("  注意: 'config.' 和 'ini' 之间有空格\r\n");
    printf("\r\n");
    printf("文件存在时:\r\n");
    printf("  输入: conf\r\n");
    printf("  输出: Ratio = xxxx\r\n");
    printf("        Limit= xxxx\r\n");
    printf("        config read success\r\n");
    printf("  注意: 'Limit=' 等号前没有空格\r\n");
    printf("\r\n");
    
    // 实际测试
    printf("实际测试结果：\r\n");
    
    // 测试文件不存在
    delete_config_ini();
    printf("文件不存在时:\r\n");
    process_uart_command("conf");
    printf("\r\n");
    
    // 测试文件存在
    create_test_config_ini();
    printf("文件存在时:\r\n");
    process_uart_command("conf");
    
    printf("\r\n=== 格式验证完成 ===\r\n");
}

/*
 * 使用说明：
 * 
 * 在main函数中调用以下函数进行测试：
 * 
 * 1. complete_logic_test();     // 完整逻辑测试
 * 2. verify_exact_format();     // 验证输出格式
 * 3. check_filesystem_status(); // 检查文件系统
 * 
 * 这些测试会验证：
 * - 文件系统是否正常工作
 * - conf命令是否正确识别
 * - 文件不存在时的错误处理
 * - 文件存在时的正确解析
 * - 输出格式是否完全符合题目要求
 * - 配置值是否正确读取和保存
 */
