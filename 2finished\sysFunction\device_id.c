/*
 * device_id.c
 * 功能：设备ID管理模块，提供设备ID的存储、读取和管理功能
 * 作者：按照宝宝的思路实现
 * 版权：Copyright (c) 2024. All rights reserved.
 */

#include "device_id.h"
#include "gd25qxx.h"  // Flash操作函数
#include "usart_app.h"  // 串口输出
#include "string.h"
#include "stdio.h"

static char g_device_id[DEVICE_ID_MAX_LENGTH] = {0};  // 设备ID缓存

/*!
    \brief      初始化设备ID模块
    \param[in]  none
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_init(void)
{
    char device_id[DEVICE_ID_MAX_LENGTH];
    
    // 尝试从Flash读取设备ID
    device_id_status_t status = device_id_read(device_id);
    
    if (status != DEVICE_ID_OK) {
        // 读取失败，设置默认设备ID
        status = device_id_set_default();
        if (status != DEVICE_ID_OK) {
            return status;
        }
        
        // 重新读取验证
        status = device_id_read(device_id);
        if (status != DEVICE_ID_OK) {
            return status;
        }
    }
    
    // 缓存设备ID
    strcpy(g_device_id, device_id);
    
    return DEVICE_ID_OK;
}

/*!
    \brief      从Flash读取设备ID
    \param[in]  device_id: 设备ID缓冲区指针
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_read(char *device_id)
{
    if (device_id == NULL) {
        return DEVICE_ID_INVALID;
    }
    
    uint8_t buffer[DEVICE_ID_MAX_LENGTH];
    
    // 从Flash读取数据
    spi_flash_buffer_read(buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LENGTH);
    
    // 检查是否为有效的设备ID（以"Device_ID:"开头）
    if (strncmp((char*)buffer, "Device_ID:", 10) != 0) {
        return DEVICE_ID_NOT_FOUND;
    }
    
    // 复制设备ID到输出缓冲区
    strncpy(device_id, (char*)buffer, DEVICE_ID_MAX_LENGTH - 1);
    device_id[DEVICE_ID_MAX_LENGTH - 1] = '\0';  // 确保字符串结束
    
    return DEVICE_ID_OK;
}

/*!
    \brief      写入设备ID到Flash
    \param[in]  device_id: 要写入的设备ID字符串
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_write(const char *device_id)
{
    if (device_id == NULL || strlen(device_id) >= DEVICE_ID_MAX_LENGTH) {
        return DEVICE_ID_INVALID;
    }
    
    uint8_t buffer[DEVICE_ID_MAX_LENGTH];
    memset(buffer, 0, sizeof(buffer));
    
    // 复制设备ID到缓冲区
    strncpy((char*)buffer, device_id, DEVICE_ID_MAX_LENGTH - 1);
    
    // 擦除Flash扇区
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);
    
    // 写入设备ID到Flash
    spi_flash_buffer_write(buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LENGTH);
    
    // 更新缓存
    strcpy(g_device_id, device_id);
    
    return DEVICE_ID_OK;
}

/*!
    \brief      设置默认设备ID
    \param[in]  none
    \param[out] none
    \retval     device_id_status_t: 操作状态
*/
device_id_status_t device_id_set_default(void)
{
    // 按照题目要求设置设备ID
    const char *default_id = "Device_ID:2025-CIMC-137766";
    return device_id_write(default_id);
}

/*!
    \brief      获取当前设备ID字符串
    \param[in]  none
    \param[out] none
    \retval     const char*: 设备ID字符串指针
*/
const char* device_id_get(void)
{
    if (strlen(g_device_id) > 0) {
        return g_device_id;
    } else {
        return "Device_ID:2025-CIMC-137766";
    }
}

/*!
    \brief      打印系统启动信息（包含设备ID）
    \param[in]  none
    \param[out] none
    \retval     none
*/
void device_id_print_startup_info(void)
{
    my_printf(DEBUG_USART, "====system init====\r\n");
    
    if (strlen(g_device_id) > 0) {
        my_printf(DEBUG_USART, "%s\r\n", g_device_id);
    } else {
        my_printf(DEBUG_USART, "Device_ID:2025-CIMC-137766\r\n");
    }
    
    my_printf(DEBUG_USART, "====system ready====\r\n");
}
