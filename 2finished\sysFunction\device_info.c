#include "mcu_cmic_gd32f470vet6.h"
#include "device_info.h"

/* Global device information structure */
static device_info_t g_device_info;

/*!
    \brief      Initialize device information
    \param[in]  none
    \param[out] none
    \retval     none
*/
void device_info_init(void)
{
    /* Initialize device ID */
    strcpy(g_device_info.device_id, DEVICE_ID_STRING);
    
    /* Get Flash ID */
    g_device_info.flash_id = spi_flash_read_id();
    
    /* Get MCU unique ID */
    get_mcu_unique_id(g_device_info.mcu_id);
    
    /* Set firmware version */
    strcpy(g_device_info.firmware_version, "V1.0.0");
}

/*!
    \brief      Get device ID string
    \param[in]  none
    \param[out] none
    \retval     const char*: device ID string
*/
const char* get_device_id(void)
{
    return g_device_info.device_id;
}

/*!
    \brief      Get Flash chip ID
    \param[in]  none
    \param[out] none
    \retval     uint32_t: Flash chip ID
*/
uint32_t get_flash_id(void)
{
    return g_device_info.flash_id;
}

/*!
    \brief      Get MCU unique ID
    \param[in]  id: pointer to store unique ID
    \param[out] none
    \retval     none
*/
void get_mcu_unique_id(uint32_t* id)
{
    if (id == NULL) {
        return;
    }
    
    /* GD32F470 unique ID is stored at these addresses */
    id[0] = *(uint32_t*)(0x1FFF7A10);  /* Unique ID register 0 */
    id[1] = *(uint32_t*)(0x1FFF7A14);  /* Unique ID register 1 */
    id[2] = *(uint32_t*)(0x1FFF7A18);  /* Unique ID register 2 */
}

/*!
    \brief      Get firmware version string
    \param[in]  none
    \param[out] none
    \retval     const char*: firmware version string
*/
const char* get_firmware_version(void)
{
    return g_device_info.firmware_version;
}

/*!
    \brief      Print device information to USART
    \param[in]  none
    \param[out] none
    \retval     none
*/
void print_device_info(void)
{
    my_printf(DEBUG_USART, "Device_ID:%s\r\n", g_device_info.device_id);
    my_printf(DEBUG_USART, "Flash_ID:0x%06X\r\n", g_device_info.flash_id);
    my_printf(DEBUG_USART, "MCU_ID:%08X-%08X-%08X\r\n", 
              g_device_info.mcu_id[0], 
              g_device_info.mcu_id[1], 
              g_device_info.mcu_id[2]);
    my_printf(DEBUG_USART, "Firmware:%s\r\n", g_device_info.firmware_version);
}
